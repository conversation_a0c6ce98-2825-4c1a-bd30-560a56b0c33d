﻿.clearfix:after {
    content: "";
    display: block;
    clear: both
}

html {
    font-family: "Noto Sans JP", sans-serif;
    font-weight: 400;
    -webkit-overflow-scrolling: touch
}

body {
    position: relative;
    font-family: "Noto Sans JP", sans-serif;
    font-weight: 400;
    letter-spacing: .125em;
    overflow-x: hidden;
    background: #fff;
    color: #414b65;
    font-size: 16px;
    line-height: 1
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Noto Sans JP", sans-serif;
    font-weight: 500
}

a {
    text-decoration: none;
    color: #000;
    display: block;
    cursor: pointer;
    transition: .2s
}

ul {
    margin: 0;
    padding: 0;
    font-size: 0;
    line-height: 0
}

li {
    list-style: none;
    font-size: 16px;
    line-height: 100%
}

img {
    width: 100%;
    height: auto;
    object-fit: cover
}

.main {
    overflow: hidden
}

.inner {
    max-width: 1680px;
    margin: 0 auto;
    width: calc(100% - 120px)
}

.flex {
    display: flex;
    flex-wrap: wrap
}

.kai {
    display: inline-block
}

.sp {
    display: none
}

.red {
    color: #c81042;
    margin-left: 3px
}

@media screen and (max-width:959px) {
    .inner {
        width: calc(100% - 30px)
    }

    .pc {
        display: none
    }

    .sp {
        display: block
    }
}

.header {
    top: 0;
    left: 0;
    width: 100%;
    z-index: 2;
    position: absolute;
    padding-right: 180px;
    box-sizing: border-box
}

#home-header {
    position: absolute;
    background: 0 0;
    box-shadow: none;
    display: none
}

.header .flex {
    justify-content: space-between;
    align-items: center
}

@media screen and (max-width:959px) {
    .header {
        padding-right: 0;
        position: fixed;
        height: 84px;
        background: rgba(255, 255, 255, .8)
    }
}

.header .left {
    position: fixed;
    top: 35px;
    left: 30px
}

.header .left a {
    position: relative
}

.header .left a img {
    width: 500px;
    object-fit: contain;
    height: 100px;
    margin-top: -30px;
}

@media screen and (max-width:959px) {
    .header .left {
        top: 33px;
        left: 15px
    }

    .header .left a img {
        height: 80px;
        width: 229px
    }
}

.header .open {
    position: absolute;
    top: 40px;
    right: 175px;
    font-size: 16px;
    color: #524a38
}

#mv-white .header .open {
    color: #fff
}

@media screen and (max-width:959px) {
    .header .open {
        display: none
    }
}

#fix-menu {
    position: absolute;
    top: 130px;
    right: 40px;
    text-align: right;
    z-index: 1
}

#fix-menu .menu a {
    position: relative;
    font-size: 12px;
    margin-bottom: 15px;
    padding-bottom: 3px;
    display: inline-block;
    color: #524a38
}

#mv-white #fix-menu .menu a {
    color: #fff
}

#fix-menu .menu a:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #524a38;
    transition: all .3s;
    transform: scale(0, 1);
    transform-origin: left top
}

#mv-white #fix-menu .menu a:after {
    background: #fff
}

#fix-menu .menu a:hover:after {
    transform: scale(1, 1)
}

@media screen and (max-width:959px) {
    #fix-menu {
        display: none
    }
}

#fix-btn {
    position: fixed;
    bottom: 30px;
    right: 40px;
    z-index: 2;
    text-align: center
}

#fix-btn.ab {
    position: absolute
}

.btn-style {
    text-align: center
}

.btn-style .txt {
    font-size: 12px;
    margin-bottom: 10px;
    color: #524a38
}

.btn-style.ab .txt {
    color: #fff
}

.btn {
    text-align: center
}

.btn a {
    padding-left: 15px;
    height: 50px;
    width: 200px;
    border-radius: 50px;
    background: #8f571f;
    color: #fff;
    border: 1px solid #8f571f
}

.btn a .btn-inner {
    position: relative;
    font-size: 16px;
    padding-left: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%
}

.btn a:hover {
    background: #fff;
    color: #8f571f
}

.btn a .btn-inner:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin: auto;
    width: 35px;
    height: 35px;
    background: url(../image/right-white.svg) no-repeat;
    background-size: 100% auto;
    transition: .2s;
    margin-right: 15px
}

.btn a:hover .btn-inner:before {
    background: url(../image/right-blown.svg) no-repeat;
    background-size: 100% auto
}

@media screen and (max-width:959px) {

    #fix-btn,
    #fix-btn.ab {
        position: fixed;
        bottom: 0;
        right: 0;
        width: 100%;
        z-index: 3
    }

    .btn-style .txt {
        display: none
    }

    #fix-btn .sp {
        display: flex
    }

    #fix-btn .btn {
        display: none;
        width: 100%
    }

    #fix-btn .btn a {
        border-radius: 0;
        width: 100%
    }

    #fix-btn .btn2 a {
        background: #414b65;
        border: 1px solid #414b65
    }

    #fix-btn .btn2 a:hover {
        background: #fff;
        color: #414b65
    }

    #fix-btn .btn a .btn-inner {
        margin: 0 auto;
        justify-content: flex-end
    }

    #fix-btn .btn1 a .btn-inner {
        max-width: 133px
    }

    #fix-btn .btn2 a .btn-inner {
        max-width: 76px
    }

    #fix-btn .btn a .btn-inner:before {
        pointer-events: none
    }

    #fix-btn .btn1 a .btn-inner:before {
        width: 18px;
        height: 15px;
        background: url(../image/mail-icon.svg) no-repeat bottom center;
        background-size: 100% auto
    }

    #fix-btn .btn1 a:hover .btn-inner:before {
        background: url(../image/mail-color.svg) no-repeat bottom center;
        background-size: 100% auto
    }

    #fix-btn .btn2 a .btn-inner:before {
        width: 17px;
        height: 18px;
        background: url(../image/tel-icon.svg) no-repeat;
        background-size: auto 100%
    }

    #fix-btn .btn2 a:hover .btn-inner:before {
        background: url(../image/tel-blue.svg) no-repeat;
        background-size: auto 100%
    }
}

.hamburger {
    position: relative;
    z-index: 3
}

.hamburger .line {
    position: fixed;
    top: 20px;
    right: 40px;
    display: flex;
    height: 60px;
    width: 60px;
    justify-content: center;
    align-items: center;
    z-index: 90;
    transition: .5s;
    cursor: pointer;
    background: #fff;
    border-radius: 60px
}

.hamburger .menu-btn:hover .line {
    background: #8f571f
}

.hamburger .line span,
.hamburger .line span:before,
.hamburger .line span:after {
    content: '';
    display: block;
    height: 1px;
    width: 28px;
    background-color: #000;
    position: absolute;
    transition: .3s
}

.hamburger .menu-btn:hover .line span,
.hamburger .menu-btn:hover .line span:before,
.hamburger .menu-btn:hover .line span:after {
    background-color: #fff
}

.hamburger .line span:before {
    bottom: 6px
}

.hamburger .line span:after {
    top: 6px
}

.hamburger .line span.active {
    background-color: rgba(255, 255, 255, 0) !important
}

.hamburger .line span.active:before {
    bottom: 0;
    transform: rotate(25.725deg)
}

.hamburger .line span.active:after {
    top: 0;
    transform: rotate(-25.725deg)
}

.hamburger .menu-btn {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 101;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #000;
    counter-increment: item
}

.hamburger .menu-btn.active .line,
#home-hamburger.normal .menu-btn.active .line {
    background: 0 0
}

.hamburger .menu {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 100;
    width: 100%;
    height: 100%;
    padding: 200px 0;
    background: linear-gradient(45deg, #524a38 20%, #d9d5c8)
}

.hamburger .menu .inner {
    max-width: 900px
}

.hamburger .menu .box {
    width: calc(100% / 3)
}

.hamburger .menu .box li a {
    font-size: 12px;
    color: #fff;
    margin-bottom: 50px
}

.hamburger .menu .box li a.toggle_btn {
    pointer-events: none
}

.hamburger .menu .box li a .txt,
.hamburger .menu .box3 .link-box li a {
    position: relative;
    display: inline-block;
    padding-bottom: 3px
}

.hamburger .menu .box li a .txt:after,
.hamburger .menu .box3 .link-box li a:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #fff;
    transition: all .3s;
    transform: scale(0, 1);
    transform-origin: left top
}

.hamburger .menu .box li a:hover .txt:after,
.hamburger .menu .box3 .link-box li a:hover:after {
    transform: scale(1, 1)
}

.hamburger .menu .box1 li:nth-child(1) a .en {
    height: 32px;
    width: auto
}

.hamburger .menu .box1 li:nth-child(2) a .en {
    height: 25px;
    width: auto;
    margin-bottom: 5px
}

.hamburger .menu .box1 li:nth-child(3) a .en {
    height: 32px;
    width: auto
}

.hamburger .menu .box1 li:nth-child(4) a .en {
    height: 24px;
    width: auto;
    margin-bottom: 5px
}

.hamburger .menu .box2 li:nth-child(1) a .en {
    height: 32px;
    width: auto
}

.hamburger .menu .box2 li:nth-child(2) a {
    padding-bottom: 1px
}

.hamburger .menu .box2 li:nth-child(2) a .en {
    height: 24px;
    width: auto;
    margin-bottom: 5px
}

.hamburger .menu .box2 li:nth-child(3) a .en {
    height: 24px;
    width: auto;
    margin-bottom: 5px
}

.hamburger .menu .box2 li:nth-child(4) a .en {
    height: 25px;
    width: auto;
    margin-bottom: 5px
}

.hamburger .menu .box li a.toggle_btn {
    position: relative;
    margin-bottom: 20px
}

.hamburger .menu .box li a.toggle_btn .icon {
    position: absolute;
    top: 10px;
    left: 186px;
    border: 1px solid #fff;
    width: 35px;
    height: 35px;
    border-radius: 35px
}

.hamburger .menu .box li a.toggle_btn .icon:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    margin: auto;
    width: 8px;
    height: 1px;
    background: #fff
}

.hamburger .menu .box li a.toggle_btn .icon:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    margin: auto;
    height: 8px;
    width: 1px;
    background: #fff
}

.hamburger .menu .box li a.toggle_btn.active .icon:after {
    display: none
}

.hamburger .menu .box li .child a {
    position: relative;
    font-size: 12px;
    margin: 0 0 5px 30px;
    padding: 0 0 3px;
    display: inline-block
}

.hamburger .menu .box li .child a:before {
    content: "";
    position: absolute;
    top: 50%;
    left: -15px;
    transform: translateY(-50%);
    margin: auto;
    background: #fff;
    width: 7px;
    height: 1px
}

.hamburger .menu .box li .child a:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #fff;
    transition: all .3s;
    transform: scale(0, 1);
    transform-origin: left top
}

.hamburger .menu .box li .child a:hover:after {
    transform: scale(1, 1)
}

.hamburger .menu .box3 .link-box {
    margin-bottom: 150px
}

.hamburger .menu .box3 .link-box li a {
    margin-bottom: 12px
}

.hamburger .menu .box3 .tel-box .open,
.hamburger .menu .box3 .tel-box .name {
    font-size: 12px;
    margin-bottom: 15px;
    color: #fff
}

.hamburger .active .active span.acv_open:after {
    height: 0;
    width: 0
}

.hamburger .menu {
    pointer-events: none;
    opacity: 0;
    transition: opacity .3s linear
}

.hamburger .menu.active {
    white-space: nowrap;
    overflow: hidden;
    pointer-events: auto;
    opacity: 1;
    -ms-overflow-style: none;
    scrollbar-width: none;
    height: 100vh
}

.hamburger .menu.active::-webkit-scrollbar {
    display: none
}

.hamburger .logo {
    top: 35px;
    left: 30px;
    position: fixed
}

.hamburger .logo img {
    height: 70px;
    width: 500px;
    object-fit: contain;
    margin-top: -15px;
}

.circle1 {
    position: fixed;
    left: -150px;
    bottom: -290px;
    width: 542px;
    height: 542px;
    border: 1px solid #97a2bb;
    border-radius: 50%;
    opacity: .7;
    z-index: -1
}

.circle2 {
    position: fixed;
    left: -148px;
    bottom: -284px;
    width: 766px;
    height: 766px;
    border: 1px solid #97a2bb;
    border-radius: 50%;
    opacity: .7;
    z-index: -1
}

.circle3 {
    position: fixed;
    left: -256px;
    bottom: -406px;
    width: 984px;
    height: 984px;
    border: 1px solid #97a2bb;
    border-radius: 50%;
    opacity: .7;
    z-index: -1
}

.circle4 {
    position: fixed;
    right: 130px;
    top: -65px;
    width: 478px;
    height: 478px;
    border: 1px solid #97a2bb;
    border-radius: 50%;
    opacity: .7;
    z-index: -1
}

.circle5 {
    position: fixed;
    right: -65px;
    top: 215px;
    width: 346px;
    height: 346px;
    border: 1px solid #97a2bb;
    border-radius: 50%;
    opacity: .7;
    z-index: -1
}

@media screen and (max-width:959px) {
    .hamburger .line {
        top: 12px;
        right: 12px
    }

    .hamburger .menu {
        padding: 113px 0
    }

    .hamburger .menu.active {
        overflow-y: scroll;
        height: 100%
    }

    .hamburger .menu .inner {
        max-width: 312px
    }

    .hamburger .menu .box1 {
        width: calc(50% + 26px)
    }

    .hamburger .menu .box2 {
        width: calc(50% - 26px)
    }

    .hamburger .menu .box li a .en {
        height: 20px
    }

    .hamburger .menu .box1 {
        margin-bottom: 75px
    }

    .hamburger .menu .box3 .link-box {
        margin-bottom: 0
    }

    .hamburger .menu .box3 .link-box li a {
        margin-bottom: 10px
    }

    .hamburger .menu .box3 .tel-box {
        display: none
    }

    .hamburger .logo {
        top: 33px;
        left: 15px
    }

    .hamburger .logo img {
        height: 60px;
        margin-top: -20px;
        width: 229px
    }

    .circle1 {
        left: -25px;
        bottom: -85px;
        width: 281px;
        height: 281px
    }

    .circle2 {
        left: -22px;
        bottom: -85px;
        width: 397px;
        height: 397px
    }

    .circle3 {
        left: -78px;
        bottom: -147px;
        width: 510px;
        height: 510px
    }

    .circle4 {
        right: -173px;
        top: -325px
    }

    .circle5 {
        right: -265px;
        top: 35px
    }

    .hamburger .menu .box1 li:nth-child(1) a .en {
        height: 27px;
        width: 48px
    }

    .hamburger .menu .box1 li:nth-child(2) a .en {
        height: 20px;
        width: 92px
    }

    .hamburger .menu .box1 li:nth-child(3) a .en {
        height: 27px;
        width: 173px
    }

    .hamburger .menu .box1 li:nth-child(4) a .en {
        height: 20px
    }

    .hamburger .menu .box li a.toggle_btn {
        margin-bottom: 15px
    }

    .hamburger .menu .box li a.toggle_btn .icon {
        top: 8px;
        left: 123px
    }

    .hamburger .menu .box2 li:nth-child(1) a .en {
        height: 26px;
        width: 130px
    }

    .hamburger .menu .box2 li:nth-child(2) a {
        padding-bottom: 3px
    }

    .hamburger .menu .box2 li:nth-child(2) a .en {
        height: 20px;
        width: 66px
    }

    .hamburger .menu .box2 li:nth-child(3) a .en {
        height: 20px;
        width: 80px
    }

    .hamburger .menu .box2 li:nth-child(4) a .en {
        height: 20px;
        width: 93px
    }
}

#footer {
    position: relative;
    border-top: 1px solid #d9d9d9;
    padding: 66px 0 7px;
    box-sizing: border-box
}

#footer .flex {
    justify-content: space-between
}

#footer .top {
    margin-bottom: 178px;
    padding: 0 150px 0 0
}

#footer .left {
    width: 50%
}

#footer .logo img {
    height: 24px;
    width: 305px;
    object-fit: contain;
}

#footer .box {
    width: 25%
}

#footer .box a {
    position: relative;
    color: #414b65;
    margin-bottom: 15px;
    display: inline-block;
    padding-bottom: 5px
}

#footer .box a:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #414b65;
    transition: all .3s;
    transform: scale(0, 1);
    transform-origin: left top
}

#footer .box a:hover:after {
    transform: scale(1, 1)
}

#footer .btn-style {
    position: absolute;
    top: 100px;
    right: 40px
}

#footer .btn-style .txt {
    color: #414b65
}

#footer .btn-style .txt {
    color: #414b65
}

#footer .btn a {
    background: #414b65;
    border: 1px solid #414b65
}

#footer .btn a:hover {
    color: #414b65;
    background: #fff
}

#footer .btn a:hover .btn-inner:before {
    background: url(../image/right-blue.svg) no-repeat
}

#footer .copy {
    font-size: 12px;
    color: #414b65;
    order: 1
}

#footer .bottom {
    align-items: center
}

#footer .menu {
    order: 2
}

#footer .menu a {
    font-size: 12px;
    color: #414b65;
    padding: 0 15px;
    border-right: 1px solid #414b65
}

#footer .menu a:hover {
    text-decoration: underline
}

#footer .menu li:last-child a {
    padding: 0 0 0 15px;
    border-right: none
}

@media screen and (max-width:1150px) {
    #footer {
        position: relative;
        border-top: 1px solid #d9d9d9;
        padding: 66px 0 0;
        box-sizing: border-box
    }

    #footer .bottom {
        width: 100%;
        justify-content: center
    }

    #footer .copy {
        background: #d9d9d9;
        text-align: center;
        width: 100%;
        padding: 5px;
        order: 2
    }

    #footer .menu {
        margin-bottom: 20px;
        text-align: center;
        order: 1
    }

    #footer .menu li {
        width: 100%
    }

    #footer .menu a {
        padding: 0;
        border-right: none;
        margin-bottom: 5px
    }

    #footer .menu li:last-child a {
        padding: 0
    }
}

@media screen and (max-width:959px) {
    #footer {
        padding: 66px 0 50px
    }

    #footer .top {
        margin-bottom: 50px;
        padding: 0;
        max-width: 295px
    }

    #footer .left {
        width: 100%;
        margin-bottom: 66px
    }

    #footer .logo img {
        height: 18px;
        width: 229px;
        margin: 0 auto
    }

    #footer .box {
        width: 50%
    }

    #footer .box2 {
        width: 37%
    }

    #footer .btn-style {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 35px
    }

    #footer .btn a .btn-inner {
        padding-left: 0
    }

    #footer .btn a {
        width: 300px;
        margin: 0 auto
    }

    #footer .btn-style .txt {
        display: block
    }
}

.btn-normal a {
    height: 35px;
    display: inline-block;
    color: #818181
}

.btn-normal-white a {
    color: #fff
}

.btn-normal a .btn-inner {
    position: relative;
    font-size: 14px;
    padding-left: 50px;
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%
}

.btn-normal a .btn-inner:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin: auto;
    width: 35px;
    height: 35px;
    background: url(../image/right-gray.svg) no-repeat;
    background-size: 100% auto;
    transition: .2s;
    margin-right: 15px
}

.btn-normal-white a .btn-inner:before {
    background: url(../image/right-white.svg) no-repeat;
    background-size: 100% auto
}

.btn-normal a .btn-inner:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50px;
    width: calc(100% - 50px);
    height: 1px;
    background: #818181;
    transition: all .3s;
    transform: scale(0, 1);
    transform-origin: left top
}

.btn-normal-white a .btn-inner:after {
    background: #fff
}

.btn-normal a:hover .btn-inner:after {
    transform: scale(1, 1)
}

.section-ti {
    margin-bottom: 70px
}

.section-ti .ti {
    font-size: 16px
}

.section-ti .en {
    width: auto
}

@media screen and (max-width:959px) {
    .btn-normal a .btn-inner {
        font-size: 16px
    }

    .section-ti {
        margin-bottom: 50px
    }

    .section-ti .ti {
        font-size: 16px
    }
}

#fv {
    position: relative;
    height: 100vh
}

#fv .movie {
    height: 100%;
    width: 100%
}

#fv .movie video {
    height: 100%;
    width: 100%;
    object-fit: cover
}

#fv .copy {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    margin: auto;
    font-size: 24px;
    line-height: 1.5;
    max-width: 1110px;
    color: #fff
}

.scrolldown1 {
    position: absolute;
    left: 30px;
    bottom: 30px;
    height: 210px;
    width: 12px;
    padding-top: 50px
}

.scrolldown1:after {
    content: "";
    position: absolute;
    top: 0;
    left: calc(50% + 2px);
    transform: translateX(-50%);
    margin: auto;
    width: 1px;
    height: 160px;
    background: #fff;
    animation: pathmove 2s ease-in-out infinite;
    margin-top: 50px;
    opacity: 0
}

.scrolldown2:after {
    background: #524a38
}

.scrolldown1:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    margin: auto;
    width: 12px;
    height: 37px;
    background: url(../image/scroll.svg) no-repeat center;
    background-size: 100% auto
}

.scrolldown2:before {
    background: url(../image/scroll-black.svg) no-repeat center;
    background-size: 100% auto
}

@keyframes pathmove {
    0% {
        height: 0;
        top: 0;
        opacity: 0
    }

    30% {
        height: 80px;
        opacity: 1
    }

    100% {
        height: 0;
        top: 160px;
        opacity: 0
    }
}

@media screen and (max-width:959px) {
    #fv .copy {
        font-size: 21px;
        line-height: 1.71;
        width: calc(100% - 90px)
    }

    .scrolldown1 {
        left: 13px;
        bottom: 20px
    }

    .scrolldown2 {
        display: none
    }
}

#top-m {
    position: relative;
    padding: 135px 0 160px
}

#top-m .flex {
    justify-content: space-between
}

#top-m .flex .img img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

#top-m .flex .ti {
    font-size: 24px;
    line-height: 1.5;
    margin-bottom: 35px;
    color: #524a38
}

#top-m .flex .text {
    line-height: 2.25;
    color: #524a38
}

#top-m .flex1 .text {
    max-width: 426px
}

#top-m .flex1 {
    margin-bottom: 90px
}

#top-m .flex1 .img {
    width: 57%;
    height: 470px
}

#top-m .flex1 .txt {
    width: 43%;
    padding: 90px 18px 0 30px;
    box-sizing: border-box
}

#top-m .flex2 {
    max-width: 1000px;
    width: calc(100% - 50px)
}

#top-m .flex2 .img {
    order: 2;
    width: 47%;
    height: 520px
}

#top-m .flex2 .txt {
    order: 1;
    width: 53%;
    padding: 90px 85px 0 0;
    box-sizing: border-box
}

#top-m .flex2 .name {
    font-size: 24px;
    margin: 35px 0 50px;
    color: #524a38
}

#top-m .flex2 .ti,
#top-m .flex2 .text,
#top-m .flex2 .name {
    margin-left: 15px
}

#top-m .flex2 .name .kt {
    font-size: 16px;
    margin-right: 15px
}

#top-m .bg {
    position: absolute;
    z-index: -1
}

#top-m .bg1 {
    top: -30px;
    left: -13px;
    max-width: 745px;
    width: 57.30769230769231vw
}

#top-m .bg2 {
    bottom: 80px;
    right: 0;
    max-width: 760px;
    width: 58.46153846153847vw
}

#top-m .bg img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

@media screen and (max-width:959px) {
    #top-m {
        padding: 0 0 50px
    }

    #top-m .flex .ti {
        font-size: 21px;
        line-height: 1.71;
        margin-bottom: 20px
    }

    #top-m .flex .text {
        font-size: 16px;
        line-height: 2
    }

    #top-m .flex1 .text {
        max-width: 100%
    }

    #top-m .flex1 {
        margin-bottom: 50px
    }

    #top-m .flex1 .img {
        width: 100%;
        min-height: 236px;
        margin-bottom: 33px;
        height: 62.93333333333333vw
    }

    #top-m .flex1 .ti {
        padding: 0 10px
    }

    #top-m .flex1 .txt {
        width: 100%;
        padding: 0
    }

    #top-m .flex1 .text {
        padding: 0 25px
    }

    #top-m .flex2 .img {
        order: 1;
        min-width: 200px;
        min-height: 260px;
        margin: 0 auto 33px auto;
        width: 53.333333333333336vw;
        height: 69.33333333333334vw
    }

    #top-m .flex2 .txt {
        order: 2;
        width: 100%;
        padding: 0
    }

    #top-m .flex2 .name {
        font-size: 24px;
        margin: 35px 0
    }

    #top-m .flex2 .ti,
    #top-m .flex2 .text,
    #top-m .flex2 .name {
        margin-left: 0
    }

    #top-m .bg {
        position: absolute;
        z-index: -1
    }

    #top-m .bg1 {
        top: -16px;
        left: -30px;
        min-width: 245px;
        width: 65.33333333333333vw
    }

    #top-m .bg2 {
        bottom: 650px;
        right: -20px;
        min-width: 245px;
        width: 65.33333333333333vw
    }
}

#top-s {
    position: relative;
    padding: 113px 0 50px;
    background: linear-gradient(90deg, #524a38, #d9d5c8)
}

#top-s:before {
    content: "";
    position: absolute;
    top: -50px;
    left: 300px;
    width: 1144px;
    height: 826px;
    background: url(../image/co.png) no-repeat;
    background-size: 100% auto
}

#top-s .inner {
    position: relative;
    max-width: 1150px;
    justify-content: space-between
}

#top-s .left {
    width: 385px
}

#top-s .section-ti .ti {
    color: #fff
}

#top-s .section-ti .en {
    height: 69px;
    margin-bottom: 10px
}

#top-s .left .txt {
    line-height: 2.25;
    margin-bottom: 65px;
    color: #fff
}

#top-s .right {
    position: relative
}

#top-s .right.pc {
    width: calc(100% - 385px)
}

#top-s .right .box {
    position: relative;
    padding: 0 12px
}

#top-s .right.pc .box {
    padding: 140px 0 0 min(287px, 22.076923076923077vw);
    max-width: 667px
}

#top-s .right.pc .box1 {
    position: absolute;
    top: -155px
}

#top-s .right.pc .box2 {
    margin: 200px 0 0 min(100px, 7.6923076923076925vw)
}

#top-s .right .box .img {
    border-radius: 10px
}

#top-s .right.pc .box .img {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 480px;
    max-height: 300px;
    width: 36.92307692307693vw;
    height: 23.076923076923077vw
}

#top-s .right.sp .box .img {
    position: relative;
    min-height: 218.75px;
    height: 58.333333333333336vw
}

#top-s .right .box .img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px
}

#top-s .right .box .img .section-sub-ti {
    position: absolute;
    bottom: 15px;
    left: 27px
}

#top-s .right.sp .box .img .section-sub-ti {
    bottom: 25px;
    left: 25px
}

#top-s .right .box .img .section-sub-ti .ti {
    font-size: 16px;
    color: #fff
}

#top-s .right .box .img .section-sub-ti .en {
    width: auto;
    margin-bottom: 10px;
    border-radius: 0
}

#top-s .right .box1 .img .section-sub-ti .en {
    height: 33px
}

#top-s .right .box2 .img .section-sub-ti .en {
    height: 68px
}

#top-s .right .box .list {
    position: relative;
    background: rgba(255, 255, 255, .9);
    border-radius: 10px;
    padding: min(20px, 1.5384615384615385vw) min(27px, 2.076923076923077vw)
}

#top-s .right.sp .box .list {
    padding: 20px 10px;
    top: -10px;
    margin: 0 10px
}

#top-s .right .box .list ul li {
    font-size: min(16px, 1.2307692307692308vw);
    position: relative;
    line-height: 2.25;
    padding-left: 18px;
    color: #524a38
}

#top-s .right.sp .box .list ul li {
    font-size: 18px;
    line-height: 2
}

#top-s .right .box .list ul li:before {
    content: "・";
    position: absolute;
    top: 0;
    left: 0
}

#top-page .swiper-button-next {
    top: 29.166666666666668vw;
    right: 0;
    width: 30px;
    height: 30px
}

#top-page .swiper-button-next:after {
    content: "";
    background: url(../image/right-btn.svg) no-repeat;
    background-size: cover;
    width: 30px;
    height: 30px
}

#top-page .swiper-button-prev {
    top: 29.166666666666668vw;
    left: 0;
    width: 30px;
    height: 30px
}

#top-page .swiper-button-prev:after {
    content: "";
    background: url(../image/left-btn.svg) no-repeat;
    background-size: cover;
    width: 30px;
    height: 30px
}

@media screen and (max-width:959px) {
    #top-s {
        padding: 85px 0 50px
    }

    #top-s .inner {
        width: 100%
    }

    #top-s .left {
        width: calc(100% - 50px);
        margin: 0 auto 60px auto
    }

    #top-s .section-ti .en {
        height: 34px
    }

    #top-s .left .txt {
        font-size: 16px;
        line-height: 2;
        margin-bottom: 40px
    }

    #top-s .right .box .img .section-sub-ti .en {
        margin-bottom: 5px
    }

    #top-s .right .box2 .img .section-sub-ti .en {
        height: 76px
    }
}

@media screen and (max-width:770px) {
    #top-s {
        padding: 85px 0 50px
    }

    #top-s .inner {
        width: 100%
    }

    #top-s:before {
        top: 40px;
        left: -75px;
        width: 830px;
        height: 1190px;
        background: url(../image/co-sp.png) no-repeat;
        background-size: 100% auto
    }
}

#top-st {
    position: relative;
    padding: 115px 0 95px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, .4), rgba(186, 193, 209, .4))
}

#top-st .inner {
    position: relative;
    max-width: 1300px;
    width: 100%
}

#top-st .section-ti {
    text-align: center
}

#top-st .section-ti .ti {
    color: #414b65
}

#top-st .section-ti .en {
    height: 91px;
    margin: 0 auto 15px auto
}

#top-st .flex {
    justify-content: space-between;
    margin-bottom: 55px
}

#top-st .flex .box {
    width: calc(100% / 3);
    text-align: center
}

#top-st .flex .box .img {
    width: 120px;
    margin: 0 auto 10px auto
}

#top-st .flex .box .img img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

#top-st .flex .box .ti {
    font-size: 24px;
    line-height: 1.5;
    margin-bottom: 10px
}

#top-st .flex .box .txt {
    line-height: 2.25;
    color: #606060
}

#top-st .btn-normal {
    text-align: center
}

@media screen and (max-width:959px) {
    #top-st {
        padding: 125px 0 60px
    }

    #top-st .section-ti .en {
        height: 45px;
        width: 287px;
        margin: 0 auto 10px auto
    }

    #top-st .flex {
        row-gap: 60px;
        margin-bottom: 90px
    }

    #top-st .flex .box {
        width: 100%
    }

    #top-st .flex .box .txt {
        font-size: 16px;
        line-height: 2
    }
}

#top-a {
    position: relative;
    padding: 125px 0 35px;
    background-image: url(../image/about-bg.webp);
    background-attachment: fixed;
    background-position: center left;
    background-size: cover;
    background-repeat: no-repeat
}

#top-a .section-ti {
    position: absolute;
    top: 125px;
    left: 50%;
    transform: translateX(-50%);
    margin: auto;
    max-width: 1100px;
    width: calc(100% - 60px)
}

#top-a .section-ti .ti {
    color: #fff
}

#top-a .section-ti .en {
    height: 69px;
    margin-bottom: 10px
}

#top-a .flex {
    position: relative;
    max-width: 932px;
    justify-content: space-between;
    column-gap: 64px
}

#top-a .flex .box {
    width: calc(50% - 32px);
    text-align: center
}

#top-a .flex .box1 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1
}

#top-a .flex .box2 {
    margin: 0 0 0 auto
}

#top-a .flex .box .img {
    max-height: 300px;
    width: 100%;
    height: 23.076923076923077vw
}

#top-a .flex .box .img img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

#top-a .flex .box1 {
    margin-top: 170px
}

#top-a .flex .box .ti {
    font-size: 24px;
    color: #fff;
    padding: 30px 0;
    background: rgba(65, 75, 101, .6)
}

#top-a .flex .box .txt {
    line-height: 2;
    color: #fff;
    padding: 10px 0;
    background: rgba(65, 75, 101, .6)
}

#top-a .flex .box .btn-normal {
    padding: 25px 0;
    text-align: center;
    background: rgba(65, 75, 101, .6)
}

@media screen and (max-width:959px) {
    #top-a {
        padding: 115px 0
    }

    #top-a .section-ti {
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        margin-bottom: 50px
    }

    #top-a .section-ti .en {
        height: 34px
    }

    #top-a .flex {
        width: 100%
    }

    #top-a .flex .box {
        width: 100%;
        padding: 0 7px
    }

    #top-a .flex .box .img {
        min-height: 218.75px;
        height: 58.333333333333336vw;
        width: 100%;
        max-width: 100%;
        max-height: 100%
    }

    #top-a .flex .box .txt {
        font-size: 16px;
        line-height: 2
    }

    #top-a .swiper-button-next,
    #top-a .swiper-button-prev {
        top: calc(92px + 29.166666666666668vw)
    }
}

#top-c {
    position: relative;
    padding: 235px 0 385px;
    background: url(../image/column-bg.webp) no-repeat;
    background-size: cover
}

#top-c .inner {
    position: relative;
    max-width: 1300px;
    width: 100%
}

#top-c .section-ti {
    text-align: center;
    margin-bottom: 35px
}

#top-c .section-ti .ti {
    color: #414b65
}

#top-c .section-ti .en {
    height: 91.2px;
    margin: 0 auto 15px auto
}

#top-c .swiper {
    margin-bottom: 70px
}

#top-c .swiper-wrapper {
    padding: 0 calc(100% / 3 / 2)
}

#top-c .swiper-wrapper .img {
    height: 24.923076923076923vw;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden
}

#top-c .swiper-wrapper .img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    transition: .3s
}

#top-c .swiper-wrapper .box:hover .img img {
    transform: scale(1.1)
}

#top-c .swiper-wrapper .date {
    font-size: 12px;
    margin: 0 0 0 7px;
    color: #414b65
}

#top-c .swiper-wrapper .new {
    display: inline-block;
    font-size: 14px;
    padding: 5px 12px;
    border-radius: 50px;
    background: #414b65;
    color: #fff;
    margin: 0 0 0 20px
}

#top-c .swiper-wrapper .ti {
    line-height: 2.25;
    margin: 0 0 0 7px;
    color: #414b65
}

#top-c .swiper-wrapper .box:hover .ti {
    text-decoration: underline
}

#top-c .btn-normal {
    text-align: center
}

@media screen and (max-width:959px) {
    #top-c {
        padding: 140px 0 500px
    }

    #top-c .section-ti .en {
        height: 45.6px;
        margin: 0 auto 10px auto
    }

    #top-c .swiper-wrapper {
        padding: 0
    }

    #top-c .swiper-wrapper .img {
        height: 66.46154666666666vw;
        margin-bottom: 10px
    }

    #top-c .swiper-wrapper .date {
        font-size: 14px
    }

    #top-c .swiper-wrapper .new {
        margin: 0 0 0 7px
    }

    #top-c .swiper-wrapper .ti {
        font-size: 16px;
        line-height: 2
    }
}

#top-n {
    position: relative;
    margin-top: -280px;
    padding: 63px 0 32px;
    border-radius: 10px;
    z-index: 1
}

#top-n:before {
    content: "";
    position: absolute;
    top: 0;
    right: 7.69%;
    background: #fff;
    height: 100%;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0px 30px 10px 0px rgba(0, 0, 0, .1)
}

#top-n .inner {
    position: relative;
    max-width: 1090px;
    justify-content: space-between;
    padding-right: 55px;
    width: calc(100% - 44px)
}

#top-n .left {
    width: 224px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 30px;
    margin-bottom: 25px
}

#top-n .section-ti .ti {
    color: #414b65
}

#top-n .section-ti .en {
    height: 68px;
    margin-bottom: 20px
}

#top-n .right {
    width: 61%
}

#top-n .right .box {
    padding-bottom: 20px;
    margin-bottom: 25px;
    border-bottom: 1px solid #e4e4e4;
    justify-content: space-between
}

#top-n .right .box:last-child {
    margin-bottom: 0
}

#top-n .right .img {
    height: 11.538461538461538vw;
    border-radius: 10px;
    overflow: hidden;
    width: 26.8%;
    max-height: 150px
}

#top-n .right .img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    transition: .3s
}

#top-n .right .box:hover .img img {
    transform: scale(1.1)
}

#top-n .right .txt {
    width: calc(73.2% - 15px)
}

#top-n .right .date {
    font-size: 12px;
    margin: 5px 0 2px;
    color: #414b65
}

#top-n .right .cate {
    display: inline-block;
    font-size: 14px;
    padding: 5px 12px;
    border-radius: 50px;
    background: #414b65;
    color: #fff;
    margin: 10px 0
}

#top-n .right .ti {
    line-height: 2.25;
    color: #606060
}

#top-n .right .box:hover .ti {
    text-decoration: underline
}

@media screen and (max-width:959px) {
    #top-n {
        margin-top: -409px;
        padding: 70px 0 55px
    }

    #top-n:before {
        right: 15px;
        background: #fff;
        box-shadow: 0px 20px 10px 0px rgba(0, 0, 0, .1)
    }

    #top-n .inner {
        padding-right: 20px;
        display: block
    }

    #top-n .left {
        width: 100%;
        padding-top: 0;
        padding-left: 10px;
        margin-bottom: 75px
    }

    #top-n .section-ti {
        margin-bottom: 0
    }

    #top-n .section-ti .en {
        height: 34px;
        margin-bottom: 10px
    }

    #top-n .btn-normal {
        text-align: center
    }

    #top-n .right {
        width: 100%;
        margin-bottom: 38px
    }

    #top-n .right .img {
        height: 22.222213333333332vw;
        width: 33.3%;
        max-height: 100%
    }

    #top-n .right .txt {
        width: calc(66.7% - 10px)
    }

    #top-n .right .date {
        margin: 0 0 2px
    }

    #top-n .right .cate {
        margin: 0 0 10px
    }

    #top-n .right .ti {
        font-size: 16px;
        line-height: 2
    }
}

#access {
    position: relative;
    padding: 18.461538461538463vw 0 110px;
    background: url(../image/access-bg.png) no-repeat top center;
    background-size: cover;
    text-align: center
}

#access .section-ti {
    text-align: center;
    margin-bottom: 35px
}

#access .section-ti .ti {
    color: #414b65
}

#access .section-ti .en {
    height: 69px;
    margin: 0 auto 20px auto
}

#access .map iframe {
    width: 100%;
    height: 320px;
    margin-bottom: 30px
}

#access .txt {
    line-height: 2.25;
    color: #414b65;
    width: calc(100% - 18px)
}

@media screen and (max-width:959px) {
    #access {
        padding: 163px 0 90px;
        background: 0 0;
        text-align: center
    }

    #access .section-ti {
        margin-bottom: 30px
    }

    #access .section-ti .en {
        height: 34px;
        width: 135px;
        margin: 0 auto 5px auto
    }

    #access .map iframe {
        height: 250px;
        margin-bottom: 25px
    }

    #access .txt {
        font-size: 16px;
        line-height: 2
    }
}

#page-fv {
    position: relative
}

#page-fv .bg {
    position: relative;
    height: 430px;
    width: 100%
}

#page-fv .bg img {
    height: 100%;
    width: 100%;
    object-fit: cover
}

#page-fv .section-ti {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    margin: auto;
    max-width: 1015px;
    width: calc(100% - 80px)
}

#page-fv .section-ti .ti {
    color: #524a38
}

#mv-white #page-fv .section-ti .ti {
    color: #fff
}

#page-fv .section-ti .en {
    width: auto
}

#page-fv .scrolldown1 {
    position: absolute;
    left: 30px;
    bottom: 30px;
    height: 210px;
    padding-top: 50px
}

#page-fv .scrolldown1:after {
    content: "";
    position: absolute;
    top: 0;
    left: calc(50% + 2px);
    transform: translateX(-50%);
    margin: auto;
    width: 1px;
    height: 160px;
    background: #524a38;
    animation: pathmove 2s ease-in-out infinite;
    margin-top: 50px;
    opacity: 0
}

#page-fv .scrolldown1:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    margin: auto;
    width: 12px;
    height: 37px;
    background: url(../image/scroll-black.svg) no-repeat center;
    background-size: 100% auto
}

@keyframes pathmove {
    0% {
        height: 0;
        top: 0;
        opacity: 0
    }

    30% {
        height: 80px;
        opacity: 1
    }

    100% {
        height: 0;
        top: 160px;
        opacity: 0
    }
}

@media screen and (max-width:959px) {
    #page-fv .bg {
        height: 250px
    }

    #page-fv .section-ti {
        top: 108px;
        transform: translateX(-50%)
    }

    #pp #page-fv .section-ti {
        top: 150px
    }

    #page-fv .scrolldown1 {
        left: 15px;
        bottom: 25px
    }
}

.breadcrumb {
    max-width: 1015px;
    margin: 0 auto;
    width: calc(100% - 120px);
    display: flex;
    flex-wrap: wrap;
    padding: 20px 0 80px;
    row-gap: 10px;
    align-items: center
}

#service .breadcrumb,
#method .breadcrumb {
    padding: 20px 0 25px
}

.breadcrumb a {
    font-size: 12px;
    color: #d9d5c8
}

.breadcrumb a:nth-child(2) {
    position: relative
}

.breadcrumb span,
.breadcrumb .current {
    position: relative;
    font-size: 12px;
    color: #524a38;
    line-height: 1.5
}

.breadcrumb a:nth-child(2):before,
.breadcrumb .current:before {
    content: " - ";
    color: #8f571f;
    display: inline-block;
    margin: 0 5px
}

@media screen and (max-width:959px) {
    .breadcrumb {
        width: calc(100% - 40px)
    }
}

.page-inner {
    max-width: 1015px;
    margin: 0 auto;
    width: calc(100% - 120px);
    padding-right: 190px
}

@media screen and (min-width:1540px) {
    .page-inner {
        padding-right: 0
    }
}

@media screen and (max-width:1140px) {
    .page-inner {
        padding-right: 190px
    }
}

@media screen and (max-width:959px) {
    .page-inner {
        padding-right: 0;
        width: calc(100% - 30px)
    }
}

.page-section-ti {
    margin-bottom: 32px
}

.page-section-ti .ti {
    font-size: 24px;
    margin-bottom: 13px;
    color: #524a38
}

.page-section-ti .en {
    width: auto
}

@media screen and (max-width:959px) {
    .page-section-ti {
        margin-bottom: 42px
    }
}

.fix-bg {
    position: relative;
    background-image: url(../image/about-bg.webp);
    background-attachment: fixed;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 500px;
    width: 100%;
    margin-bottom: 80px
}

#service .fix-bg {
    background-image: url(../image/service-img.webp)
}

#method .fix-bg {
    background-image: url(/wp/wp-content/themes/tasku/images/method.webp)
}

@media screen and (max-width:959px) {
    .fix-bg {
        height: 53.333333333333336vw;
        min-height: 200px;
        margin-bottom: 25px;
        background-attachment: inherit
    }
}

.page-style #page-fv .section-ti .ti {
    font-size: 24px;
    line-height: 1.5
}

.page-style .content {
    margin-bottom: 100px
}

.page-style .content.center {
    text-align: center
}

.page-style .content h2 {
    font-size: 24px;
    line-height: 1.5;
    margin: 100px 0 30px;
    color: #414b65
}

.page-style .content h3 {
    font-size: 18px;
    line-height: 2;
    margin: 50px 0 15px;
    color: #414b65
}

.page-style .content h2:first-child {
    margin-top: 0
}

.page-style .content ul {
    margin-bottom: 30px
}

.page-style .content ul li {
    position: relative;
    line-height: 2.25;
    padding-left: 18px;
    color: #524a38
}

.page-style .content ol li {
    list-style: decimal;
    margin-left: 35px;
    position: relative;
    line-height: 2.25;
    color: #524a38
}

.page-style .content ul li:before {
    content: "・";
    position: absolute;
    top: 0;
    left: 0
}

.page-style .content p {
    line-height: 2.25;
    color: #606060;
    margin-bottom: 30px
}

.page-style .content a {
    display: inline-block;
    color: #414b65;
    text-decoration: underline
}

.page-style .content hr:first-child {
    opacity: 0
}

.page-style .content hr {
    border-block-start: 1px solid #e4e4e4
}

@media screen and (max-width:959px) {
    .page-style #page-fv .section-ti .ti {
        font-size: 16px
    }

    .page-style .content h2 {
        margin: 60px 0 20px
    }

    .page-style .content p {
        font-size: 16px;
        line-height: 2
    }
}

#message {
    margin-bottom: 70px
}

#message #page-fv .section-ti .en {
    height: 54px;
    margin-bottom: 10px
}

@media screen and (max-width:959px) {
    #message #page-fv .section-ti .en {
        height: 45px;
        width: 162px;
        margin-bottom: 0
    }
}

#message .page-section-ti .en {
    height: 13px;
    width: 135px
}

#message .flex {
    padding-bottom: 47px;
    margin-bottom: 37px;
    border-bottom: 1px solid #eaedf0;
    justify-content: space-between;
    column-gap: 30px
}

#message .flex3 {
    padding: 0;
    margin-bottom: 0;
    border-bottom: none
}

#message .flex2 .img {
    order: 2
}

#message .flex2 .txt {
    order: 1
}

#message .flex .img {
    width: 42%
}

#message .flex .img img {
    height: 100%
}

#message .flex1 .img {
    width: 52%
}

#message .flex .txt {
    width: calc(58% - 30px);
    line-height: 2.25;
    color: #524a38
}

#message .flex1 .txt {
    width: calc(48% - 30px)
}

@media screen and (max-width:959px) {
    #message {
        margin-bottom: 85px
    }

    #message .page-inner {
        width: 100%
    }

    #message .page-section-ti {
        width: calc(100% - 30px);
        margin: 0 auto 42px auto
    }

    #message .flex {
        padding-bottom: 35px;
        margin-bottom: 40px;
        width: 100%
    }

    #message .flex3 {
        padding: 0;
        margin-bottom: 0
    }

    #message .flex2 .img {
        order: 1
    }

    #message .flex2 .txt {
        order: 2
    }

    #message .flex .img {
        width: 100%;
        margin-bottom: 30px
    }

    #message .flex1 .img {
        width: 100%
    }

    #message .flex3 .img {
        width: 53.333333333333336vw;
        height: 69.33333333333334vw;
        min-width: 200px;
        min-height: 260px;
        margin: 0 auto 30px auto
    }

    #message .flex .txt {
        font-size: 16px;
        width: calc(100% - 30px);
        line-height: 2;
        margin: 0 auto
    }

    #message .flex1 .txt {
        width: calc(100% - 30px)
    }
}

#service #page-fv .section-ti .en {
    height: 42px;
    width: 194px;
    margin-bottom: 25px
}

#service .page-section-ti .en {
    height: 13px;
    width: 218px
}

#service .content h2 {
    font-size: 24px;
    line-height: 1.5;
    padding-bottom: 40px;
    margin-bottom: 35px;
    color: #524a38;
    border-bottom: 1px solid #eaedf0
}

#service .content p {
    line-height: 2.25;
    margin-bottom: 30px;
    color: #524a38
}

#service .content img {
    width: 100%;
    margin-bottom: 77px
}

@media screen and (max-width:959px) {
    #service #page-fv .section-ti .en {
        height: 35px;
        width: 162px;
        margin-bottom: 10px
    }

    #service .content h2 {
        padding-bottom: 20px;
        margin-bottom: 15px
    }

    #service .content p {
        font-size: 16px;
        line-height: 2;
        margin-bottom: 20px;
        color: #524a38
    }

    #service .content img {
        margin-bottom: 53px
    }
}

#method {
    padding-top: 73px;
    margin-bottom: 100px
}

#method .section-ti .en {
    height: 55px
}

#method .flex {
    padding-bottom: 65px;
    margin-bottom: 60px;
    border-bottom: 1px solid #eaedf0;
    justify-content: space-between
}

#method .flex3 {
    padding: 0;
    margin-bottom: 0;
    border-bottom: none
}

#method .flex .img {
    width: 162px
}

#method .flex .txt {
    width: calc(100% - 172px);
    padding-top: 30px
}

#method .flex h2 {
    font-size: 24px;
    line-height: 1.5;
    margin-bottom: 15px;
    color: #414b65
}

#method .flex p {
    line-height: 2.25;
    color: #606060
}

@media screen and (max-width:959px) {
    #method {
        padding-top: 84px;
        margin-bottom: 95px
    }

    #method .page-inner {
        width: 100%
    }

    #method .section-ti .en {
        height: 46px
    }

    #method .flex {
        padding-bottom: 40px;
        margin-bottom: 15px;
        justify-content: center
    }

    #method .flex .img {
        width: 162px;
        margin: 10px auto
    }

    #method .flex .txt {
        width: 100%;
        padding-top: 0
    }

    #method .flex h2 {
        text-align: center
    }

    #method .flex p {
        font-size: 16px;
        line-height: 2;
        padding: 0 15px
    }
}

#members {
    margin-bottom: 35px
}

#members .page-inner {
    max-width: 1045px;
    width: calc(100% - 90px)
}

#members #page-fv .section-ti .en {
    height: 42px;
    margin-bottom: 20px
}

#members .content .txt {
    line-height: 2.25;
    color: #606060;
    margin-bottom: 40px;
    padding: 0 15px
}

@media screen and (max-width:959px) {
    #members .page-inner {
        width: 100%
    }

    #members #page-fv .section-ti .en {
        height: 35px;
        width: 210px;
        margin-bottom: 10px
    }

    #members .content .txt {
        font-size: 16px;
        line-height: 2
    }
}

.column,
.news {
    margin-bottom: 100px
}

.column #page-fv .section-ti .en {
    height: 54px
}

.news #page-fv .section-ti .en {
    height: 42px;
    width: 137px;
    margin-bottom: 20px
}

.column .flex {
    position: relative;
    justify-content: space-between;
    column-gap: 30px;
    row-gap: 30px
}

.column .flex:after {
    content: "";
    display: block;
    width: calc(100% / 3 - 30px)
}

.column .flex .box {
    width: calc(100% / 3 - 30px)
}

.column .flex .img {
    height: 15vw;
    max-height: 260px;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden
}

.column .flex .img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    transition: .3s
}

.column .flex .box .link:hover .img img {
    transform: scale(1.1)
}

.column .flex .date {
    font-size: 12px;
    margin: 0 0 0 7px;
    color: #414b65
}

.column .flex .new {
    display: inline-block;
    font-size: 14px;
    padding: 5px 12px;
    border-radius: 50px;
    background: #414b65;
    color: #fff;
    margin: 0 0 0 20px
}

.column .flex .ti {
    line-height: 1.5;
    margin: 10px 0 15px 7px;
    color: #414b65
}

.column .flex .box .link:hover .ti {
    text-decoration: underline
}

.column .pdf-link {
    position: relative;
    font-size: 16px;
    margin: 0 0 5px 25px;
    padding: 0 0 3px;
    display: inline-block;
    color: #414b65
}

.column.single .pdf-link {
    margin: 0 0 5px 18px
}

.column .pdf-link:before {
    content: "";
    position: absolute;
    top: 50%;
    left: -15px;
    transform: translateY(-50%);
    margin: auto;
    background: #414b65;
    width: 7px;
    height: 1px
}

.column .flex .pdf-link:hover {
    text-decoration: underline
}

@media screen and (max-width:959px) {
    .column #page-fv .section-ti .en {
        height: 46px
    }

    .news #page-fv .section-ti .en {
        height: 35px;
        width: 114px;
        margin-bottom: 10px
    }

    .news #page-fv .section-ti .ti {
        font-size: 16px
    }

    .column .flex:after {
        display: none
    }

    .column .flex .box {
        width: 100%
    }

    .column .flex .img {
        height: 66.46154666666666vw;
        max-height: 100%
    }

    .column .flex .date {
        font-size: 14px
    }

    .column .flex .new {
        margin: 0 0 0 7px
    }

    .column .flex .ti {
        font-size: 16px;
        line-height: 2
    }
}

.pagination-center {
    text-align: center;
    margin-top: min(115px, 5.989583333333334vw)
}

.pagination {
    position: relative;
    text-align: center;
    font-size: 0;
    display: inline-block
}

.page-numbers {
    font-family: "Inter", sans-serif;
    font-weight: 400;
    font-size: 18px;
    display: inline-block;
    width: 39px;
    line-height: normal;
    vertical-align: top;
    color: #d9d5c8
}

.page-numbers.next,
.page-numbers.prev {
    font-size: 16px
}

.page-numbers:hover {
    text-decoration: underline
}

.pagination .current {
    color: #524a38
}

@media screen and (max-width:959px) {
    .pagination-center {
        margin-top: 59px
    }

    .page-numbers {
        font-size: 16px;
        width: 25px
    }
}

.single .thumb {
    margin-bottom: 30px
}

.single .thumb img {
    border-radius: 10px
}

.column.single .flex .date {
    margin-left: 0
}

.single .dc-flex {
    margin-bottom: 50px
}

.single .pr-nx {
    border-top: 1px solid #eaedf0;
    padding: 30px 0 50px;
    justify-content: space-between;
    column-gap: 20px
}

.single .pr-nx .box {
    width: calc(50% - 20px);
    line-height: 1.5;
    max-width: 200px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden
}

.single .pr-nx .box a {
    transition: 0s
}

.single .pr-nx .box a:hover {
    text-decoration: underline
}

.single .pr-nx .nx {
    text-align: right
}

.single .btn-normal {
    text-align: center
}

@media screen and (max-width:959px) {
    .single .pr-nx .box a {
        font-size: 14px
    }
}

#news-a .box {
    padding-bottom: 20px;
    margin-bottom: 25px;
    border-bottom: 1px solid #e4e4e4;
    justify-content: space-between
}

#news-a .box:last-child {
    margin-bottom: 0
}

#news-a .img {
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    width: 200px
}

#news-a .img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    transition: .3s
}

#news-a .box:hover .img img {
    transform: scale(1.1)
}

#news-a .txt {
    width: calc(100% - 215px)
}

.news .date {
    font-size: 12px;
    margin: 5px 0 2px;
    color: #414b65
}

.single.news .date {
    margin-bottom: 0
}

.news .cate {
    display: inline-block;
    font-size: 14px;
    padding: 5px 12px;
    border-radius: 50px;
    background: #414b65;
    color: #fff;
    margin: 10px 0
}

.single.news .cate {
    line-height: 1
}

#news-a .ti {
    line-height: 2.25;
    color: #606060
}

#news-a .box:hover .ti {
    text-decoration: underline
}

@media screen and (max-width:959px) {
    #news-a .img {
        height: 22.222213333333332vw;
        width: 33.3%;
        max-height: 100%
    }

    #news-a .txt {
        width: calc(66.7% - 10px)
    }

    #news-a .date {
        margin: 0 0 2px
    }

    .single.news .date {
        font-size: 12px
    }

    #news-a .cate {
        margin: 0 0 10px
    }

    #news-a .ti {
        font-size: 16px;
        line-height: 2
    }
}

#company {
    margin-bottom: 100px
}

#company .section-ti .en {
    height: 55px;
    margin-bottom: 13px
}

#company .page-inner.area1,
#company .page-inner.area2 {
    padding-bottom: 65px;
    margin-bottom: 100px;
    border-bottom: 1px solid #e4e4e4
}

#company .page-inner .content {
    margin-bottom: 0
}

#company .area1 .page-section-ti .en {
    height: 13px;
    width: 124px
}

#company .area2 .page-section-ti .en {
    height: 10px;
    width: 111px
}

#company .area3 .page-section-ti .en {
    height: 10px;
    width: 40px
}

#company .page-inner table td {
    padding: 20px 0;
    line-height: 2.25;
    vertical-align: top
}

#company .page-inner table td:first-child {
    width: 200px;
    color: #414b65
}

#company .map iframe {
    width: 100%;
    height: 320px
}

@media screen and (max-width:959px) {
    #company {
        margin-bottom: 85px
    }

    #company .section-ti .en {
        height: 45px;
        margin-bottom: 0
    }

    #company .page-inner.area1,
    #company .page-inner.area2 {
        padding-bottom: 65px;
        margin-bottom: 60px
    }

    #company .page-inner table td {
        font-size: 16px;
        padding: 20px 0;
        line-height: 2
    }

    #company .map iframe {
        height: 250px
    }
}

#contact #page-fv .section-ti .en {
    height: 42px;
    margin-bottom: 25px
}

#contact .content label {
    position: relative;
    margin-bottom: 20px;
    display: block
}

#contact .content input[type=text],
#contact .content input[type=email],
#contact .content input[type=tel],
#contact .content textarea {
    border: 1px solid #eaedf0;
    border-radius: 5px;
    background: #fff;
    width: 100%
}

#contact .content input[type=text],
#contact .content input[type=email],
#contact .content input[type=tel] {
    height: 54px;
    margin-top: 11px
}

#contact .content .wpcf7-radio {
    display: block;
    margin-top: 11px
}

#contact .content .wpcf7-radio label {
    margin-bottom: 0
}

#contact .content .wpcf7-list-item {
    margin: 0 50px 0 0
}

.wpcf7-list-item.first {
    margin: 0
}

.wpcf7-list-item-label {
    cursor: pointer;
    display: flex
}

input[type=radio] {
    opacity: 0;
    position: absolute
}

.wpcf7-list-item-label:before {
    background: #fff;
    border: 1px solid #eaedf0;
    border-radius: 100%;
    content: "";
    height: 1.2em;
    margin-bottom: auto;
    margin-right: .5em;
    margin-top: auto;
    width: 1.2em
}

input[type=radio]:checked+.wpcf7-list-item-label:before {
    background-color: #414b65;
    box-shadow: inset 0 0 0 3px #fff
}

#contact .content textarea {
    height: 467px;
    margin: 11px 0 0
}

#contact .content .btn {
    text-align: center
}

#contact .content input[type=submit] {
    font-size: 16px;
    height: 50px;
    width: 200px;
    border-radius: 50px;
    color: #fff;
    background: #414b65;
    border: 1px solid #414b65;
    transition: .3s;
    cursor: pointer
}

#contact .content input[type=submit]:hover {
    background: #fff;
    color: #414b65
}

#contact .content .wpcf7-spinner {
    display: none
}

@media screen and (max-width:959px) {
    #contact #page-fv .section-ti .en {
        height: 35px;
        width: 163px;
        margin-bottom: 10px
    }
}