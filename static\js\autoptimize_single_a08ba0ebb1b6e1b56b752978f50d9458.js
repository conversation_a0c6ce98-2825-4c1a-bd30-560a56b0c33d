﻿$(function(){var fixedHeader=$('#fixed-header');fixedHeader.hide();$(window).scroll(function(){if($(this).scrollTop()>80){fixedHeader.fadeIn();}else{fixedHeader.fadeOut();}});});const setFillHeight=()=>{const vh=window.innerHeight*0.01;document.documentElement.style.setProperty('--vh',`${vh}px`);}
window.addEventListener('resize',setFillHeight);setFillHeight();$(function(){$('.menu-btn').on('click',function(){$('.menu').toggleClass('active');});}());$(function(){$('.menu-btn').on('click',function(){$('span').toggleClass('active');});}());$(function(){$('.menu-btn').on('click',function(){$('.menu-btn').toggleClass('active');});}());$(function(){$('.menu a').on('click',function(){$('.menu').removeClass('active');$('span').removeClass('active');$('.menu-btn').removeClass('active');});}());