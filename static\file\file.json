{"name": "タスク・アドバイザーズ株式会社", "description": "投資家としての長年の実務経験と 培ったグローバル・ネットワークを活かし お客様の側に立ったサービスを提供します", "url": "https://www.tasku.co.jp/wp", "home": "https://www.tasku.co.jp", "gmt_offset": 9, "timezone_string": "Asia/Tokyo", "page_for_posts": 0, "page_on_front": 2, "show_on_front": "page", "namespaces": ["oembed/1.0", "aioseo/v1", "contact-form-7/v1", "redirection/v1", "wp/v2", "wp-site-health/v1", "wp-block-editor/v1"], "authentication": {"application-passwords": {"endpoints": {"authorization": "https://www.tasku.co.jp/wp/wp-admin/authorize-application.php"}}}, "routes": {"/": {"namespace": "", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/"}]}}, "/batch/v1": {"namespace": "", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"validation": {"type": "string", "enum": ["require-all-validate", "normal"], "default": "normal", "required": false}, "requests": {"type": "array", "maxItems": 25, "items": {"type": "object", "properties": {"method": {"type": "string", "enum": ["POST", "PUT", "PATCH", "DELETE"], "default": "POST"}, "path": {"type": "string", "required": true}, "body": {"type": "object", "properties": [], "additionalProperties": true}, "headers": {"type": "object", "properties": [], "additionalProperties": {"type": ["string", "array"], "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/batch/v1"}]}}, "/oembed/1.0": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "oembed/1.0", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/oembed/1.0"}]}}, "/oembed/1.0/embed": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "oEmbed データを取得するリソースの URL。", "type": "string", "format": "uri", "required": true}, "format": {"default": "json", "required": false}, "maxwidth": {"default": 600, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/oembed/1.0/embed"}]}}, "/oembed/1.0/proxy": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "oEmbed データを取得するリソースの URL。", "type": "string", "format": "uri", "required": true}, "format": {"description": "使用する oEmbed 形式。", "type": "string", "default": "json", "enum": ["json", "xml"], "required": false}, "maxwidth": {"description": "埋め込みフレームの最大幅 (ピクセル)。", "type": "integer", "default": 600, "required": false}, "maxheight": {"description": "埋め込みフレームの最大の高さ (ピクセル)。", "type": "integer", "required": false}, "discover": {"description": "許可されていないプロバイダーに対して oEmbed 検出リクエストを行うかどうか。", "type": "boolean", "default": true, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/oembed/1.0/proxy"}]}}, "/aioseo/v1": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "aioseo/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1"}]}}, "/aioseo/v1/options": {"namespace": "aioseo/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/options"}]}}, "/aioseo/v1/ping": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/ping"}]}}, "/aioseo/v1/post": {"namespace": "aioseo/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/post"}]}}, "/aioseo/v1/post/(?P<postId>[\\d]+)/first-attached-image": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/aioseo/v1/user/(?P<userId>[\\d]+)/image": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/aioseo/v1/tags": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/tags"}]}}, "/aioseo/v1/search-statistics/url/auth": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/search-statistics/url/auth"}]}}, "/aioseo/v1/search-statistics/url/reauth": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/search-statistics/url/reauth"}]}}, "/aioseo/v1/writing-assistant/keyword/(?P<postId>[\\d]+)": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/aioseo/v1/writing-assistant/user-info": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/user-info"}]}}, "/aioseo/v1/writing-assistant/user-options": {"namespace": "aioseo/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/user-options"}]}}, "/aioseo/v1/writing-assistant/report-history": {"namespace": "aioseo/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/report-history"}]}}, "/aioseo/v1/htaccess": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/htaccess"}]}}, "/aioseo/v1/post/(?P<postId>[\\d]+)/disable-primary-term-education": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/aioseo/v1/post/(?P<postId>[\\d]+)/disable-link-format-education": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/aioseo/v1/post/(?P<postId>[\\d]+)/update-internal-link-count": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/aioseo/v1/post/(?P<postId>[\\d]+)/process-content": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/aioseo/v1/posts-list/load-details-column": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/posts-list/load-details-column"}]}}, "/aioseo/v1/posts-list/update-details-column": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/posts-list/update-details-column"}]}}, "/aioseo/v1/terms-list/load-details-column": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/terms-list/load-details-column"}]}}, "/aioseo/v1/terms-list/update-details-column": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/terms-list/update-details-column"}]}}, "/aioseo/v1/keyphrases": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/keyphrases"}]}}, "/aioseo/v1/analyze": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/analyze"}]}}, "/aioseo/v1/analyze-headline": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/analyze-headline"}]}}, "/aioseo/v1/analyze-headline/delete": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/analyze-headline/delete"}]}}, "/aioseo/v1/analyze/delete-site": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/analyze/delete-site"}]}}, "/aioseo/v1/clear-log": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/clear-log"}]}}, "/aioseo/v1/connect": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/connect"}]}}, "/aioseo/v1/connect-pro": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/connect-pro"}]}}, "/aioseo/v1/connect-url": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/connect-url"}]}}, "/aioseo/v1/backup": {"namespace": "aioseo/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": []}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/backup"}]}}, "/aioseo/v1/backup/restore": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/backup/restore"}]}}, "/aioseo/v1/email-debug-info": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/email-debug-info"}]}}, "/aioseo/v1/migration/fix-blank-formats": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/migration/fix-blank-formats"}]}}, "/aioseo/v1/notification/blog-visibility-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/blog-visibility-reminder"}]}}, "/aioseo/v1/notification/conflicting-plugins-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/conflicting-plugins-reminder"}]}}, "/aioseo/v1/notification/description-format-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/description-format-reminder"}]}}, "/aioseo/v1/notification/email-reports-enable": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/email-reports-enable"}]}}, "/aioseo/v1/notification/install-addons-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-addons-reminder"}]}}, "/aioseo/v1/notification/install-aioseo-image-seo-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-aioseo-image-seo-reminder"}]}}, "/aioseo/v1/notification/install-aioseo-local-business-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-aioseo-local-business-reminder"}]}}, "/aioseo/v1/notification/install-aioseo-news-sitemap-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-aioseo-news-sitemap-reminder"}]}}, "/aioseo/v1/notification/install-aioseo-video-sitemap-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-aioseo-video-sitemap-reminder"}]}}, "/aioseo/v1/notification/install-mi-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-mi-reminder"}]}}, "/aioseo/v1/notification/install-om-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/install-om-reminder"}]}}, "/aioseo/v1/notification/v3-migration-custom-field-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/v3-migration-custom-field-reminder"}]}}, "/aioseo/v1/notification/v3-migration-schema-number-reminder": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notification/v3-migration-schema-number-reminder"}]}}, "/aioseo/v1/notifications/dismiss": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/notifications/dismiss"}]}}, "/aioseo/v1/objects": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/objects"}]}}, "/aioseo/v1/plugins/deactivate": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/plugins/deactivate"}]}}, "/aioseo/v1/plugins/install": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/plugins/install"}]}}, "/aioseo/v1/plugins/upgrade": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/plugins/upgrade"}]}}, "/aioseo/v1/reset-settings": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/reset-settings"}]}}, "/aioseo/v1/search-statistics/sitemap/delete": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/search-statistics/sitemap/delete"}]}}, "/aioseo/v1/search-statistics/sitemap/ignore": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/search-statistics/sitemap/ignore"}]}}, "/aioseo/v1/settings/export": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/export"}]}}, "/aioseo/v1/settings/export-content": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/export-content"}]}}, "/aioseo/v1/settings/hide-setup-wizard": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/hide-setup-wizard"}]}}, "/aioseo/v1/settings/hide-upgrade-bar": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/hide-upgrade-bar"}]}}, "/aioseo/v1/settings/import": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/import"}]}}, "/aioseo/v1/settings/import/(?P<siteId>[\\d]+)": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/aioseo/v1/settings/import-plugins": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/import-plugins"}]}}, "/aioseo/v1/settings/toggle-card": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/toggle-card"}]}}, "/aioseo/v1/settings/toggle-radio": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/toggle-radio"}]}}, "/aioseo/v1/settings/dismiss-alert": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/dismiss-alert"}]}}, "/aioseo/v1/settings/items-per-page": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/items-per-page"}]}}, "/aioseo/v1/settings/semrush-country": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/semrush-country"}]}}, "/aioseo/v1/settings/do-task": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/settings/do-task"}]}}, "/aioseo/v1/sitemap/deactivate-conflicting-plugins": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/sitemap/deactivate-conflicting-plugins"}]}}, "/aioseo/v1/sitemap/delete-static-files": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/sitemap/delete-static-files"}]}}, "/aioseo/v1/sitemap/validate-html-sitemap-slug": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/sitemap/validate-html-sitemap-slug"}]}}, "/aioseo/v1/tools/delete-robots-txt": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/tools/delete-robots-txt"}]}}, "/aioseo/v1/tools/import-robots-txt": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/tools/import-robots-txt"}]}}, "/aioseo/v1/wizard": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/wizard"}]}}, "/aioseo/v1/integration/semrush/authenticate": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/integration/semrush/authenticate"}]}}, "/aioseo/v1/integration/semrush/refresh": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/integration/semrush/refresh"}]}}, "/aioseo/v1/integration/semrush/keyphrases": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/integration/semrush/keyphrases"}]}}, "/aioseo/v1/integration/wpcode/snippets": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/integration/wpcode/snippets"}]}}, "/aioseo/v1/crawl-cleanup": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/crawl-cleanup"}]}}, "/aioseo/v1/crawl-cleanup/block": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/crawl-cleanup/block"}]}}, "/aioseo/v1/crawl-cleanup/delete-blocked": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/crawl-cleanup/delete-blocked"}]}}, "/aioseo/v1/crawl-cleanup/delete-unblocked": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/crawl-cleanup/delete-unblocked"}]}}, "/aioseo/v1/email-summary/send": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/email-summary/send"}]}}, "/aioseo/v1/writing-assistant/process": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/process"}]}}, "/aioseo/v1/writing-assistant/content-analysis": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/content-analysis"}]}}, "/aioseo/v1/writing-assistant/disconnect": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/disconnect"}]}}, "/aioseo/v1/writing-assistant/set-report-progress": {"namespace": "aioseo/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/writing-assistant/set-report-progress"}]}}, "/aioseo/v1/search-statistics/auth": {"namespace": "aioseo/v1", "methods": ["DELETE"], "endpoints": [{"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/aioseo/v1/search-statistics/auth"}]}}, "/contact-form-7/v1": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "contact-form-7/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/contact-form-7/v1"}]}}, "/contact-form-7/v1/contact-forms": {"namespace": "contact-form-7/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/contact-form-7/v1/contact-forms"}]}}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)": {"namespace": "contact-form-7/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}, {"methods": ["DELETE"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/feedback": {"namespace": "contact-form-7/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/feedback/schema": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/refill": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/redirection/v1": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "redirection/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1"}]}}, "/redirection/v1/redirect": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/redirect"}]}}, "/redirection/v1/redirect/(?P<id>[\\d]+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/redirection/v1/redirect/post": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"text": {"description": "Text to match", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/redirect/post"}]}}, "/redirection/v1/bulk/redirect/(?P<bulk>delete|enable|disable|reset)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "global": {"description": "Apply bulk action globally, as per filters", "type": "boolean", "required": false}, "items": {"description": "Array of IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/group": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "moduleId": {"description": "Module ID", "type": "integer", "minimum": 0, "maximum": 3, "required": true}, "name": {"description": "Group name", "type": "string", "required": true}, "status": {"description": "Status of the group", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/group"}]}}, "/redirection/v1/group/(?P<id>[\\d]+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"moduleId": {"description": "Module ID", "type": "integer", "minimum": 0, "maximum": 3, "required": true}, "name": {"description": "Group name", "type": "string", "required": true}, "status": {"description": "Status of the group", "required": false}}}]}, "/redirection/v1/bulk/group/(?P<bulk>delete|enable|disable)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/log": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/log"}]}}, "/redirection/v1/bulk/log/(?P<bulk>delete)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/404": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/404"}]}}, "/redirection/v1/bulk/404/(?P<bulk>delete)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/setting": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/setting"}]}}, "/redirection/v1/plugin": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"name": {"description": "Name", "type": "string", "required": false}, "value": {"description": "Value", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/plugin"}]}}, "/redirection/v1/plugin/delete": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/plugin/delete"}]}}, "/redirection/v1/plugin/test": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/plugin/test"}]}}, "/redirection/v1/plugin/data": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"upgrade": {"description": "Upgrade parameter", "type": "string", "enum": ["stop", "skip", "retry"], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/plugin/data"}]}}, "/redirection/v1/import/file/(?P<group_id>\\d+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/redirection/v1/import/plugin": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/redirection/v1/import/plugin"}]}}, "/redirection/v1/export/(?P<module>1|2|3|all)/(?P<format>csv|apache|nginx|json)": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/wp/v2": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp/v2", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2"}]}}, "/wp/v2/posts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "特定の投稿者に割り当てられた投稿に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "特定の投稿者が割り当てられた投稿を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "結果セットを複数のタクソノミー間の関係に基づいて限定します。", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "「categories」タクソノミーに割り当てられた特定のタームを持つ項目に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "結果を限定したタームに子タームを含めるかどうか。", "type": "boolean", "default": false}, "operator": {"description": "特定のタームのすべてに項目を割り当てるか、一部に割り当てるか。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "「categories」タクソノミーに割り当てられた特定のタームを持つ項目以外に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "結果を限定したタームに子タームを含めるかどうか。", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}, "tags": {"description": "「tags」タクソノミーに割り当てられた特定のタームを持つ項目に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "特定のタームのすべてに項目を割り当てるか、一部に割り当てるか。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "tags_exclude": {"description": "「tags」タクソノミーに割り当てられた特定のタームを持つ項目以外に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "sticky": {"description": "先頭固定表示の項目に結果を限定します。", "type": "boolean", "required": false}, "ignore_sticky": {"description": "固定表示投稿を無視するかどうか。", "type": "boolean", "default": true, "required": false}, "format": {"description": "1つ以上のフォーマットが割り当てられた項目に結果セットを限定します。", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "投稿のフォーマット。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "投稿を先頭固定表示にするかどうか。", "type": "boolean", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "categories": {"description": "タクソノミー「category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "タクソノミー「post_tag」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/posts"}]}}, "/wp/v2/posts/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "デフォルトの抜粋の長さを上書きします。", "type": "integer", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "投稿のフォーマット。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "投稿を先頭固定表示にするかどうか。", "type": "boolean", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "categories": {"description": "タクソノミー「category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "タクソノミー「post_tag」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "リビジョンがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/posts/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "投稿のフォーマット。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "投稿を先頭固定表示にするかどうか。", "type": "boolean", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "categories": {"description": "タクソノミー「category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "タクソノミー「post_tag」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/pages": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "特定の投稿者に割り当てられた投稿に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "特定の投稿者が割り当てられた投稿を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "特定の menu_order 値を持つ投稿に結果を限定します。", "type": "integer", "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "parent": {"description": "特定の親 ID に属する項目に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "特定の親 ID に属さない項目に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "parent": {"description": "親投稿の ID。", "type": "integer", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "他の投稿との関連による、投稿の順番。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/pages"}]}}, "/wp/v2/pages/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "デフォルトの抜粋の長さを上書きします。", "type": "integer", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "parent": {"description": "親投稿の ID。", "type": "integer", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "他の投稿との関連による、投稿の順番。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "リビジョンがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/pages/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親投稿の ID。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "他の投稿との関連による、投稿の順番。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/media": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "特定の投稿者に割り当てられた投稿に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "特定の投稿者が割り当てられた投稿を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "特定の親 ID に属する項目に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "特定の親 ID に属さない項目に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "inherit", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["inherit", "private", "trash"], "type": "string"}, "required": false}, "media_type": {"default": null, "description": "特定のメディアタイプが割り当てられた添付ファイルに結果を限定します。", "type": "string", "enum": ["image", "video", "text", "application", "audio"], "required": false}, "mime_type": {"default": null, "description": "特定の MIME タイプが割り当てられた添付ファイルに結果を限定します。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "alt_text": {"description": "添付ファイルが表示されないときに表示する代替テキスト。", "type": "string", "required": false}, "caption": {"description": "添付ファイルのキャプション。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、添付ファイルのキャプション。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、添付ファイルの HTML キャプション。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "添付ファイルの説明。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、添付ファイルの説明。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、添付ファイルの HTML の説明。", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "添付ファイルの関連投稿の ID。", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/media"}]}}, "/wp/v2/media/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "投稿者の ID。", "type": "integer", "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "comment_status": {"description": "投稿がコメントを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "投稿がピンバックを受け付けているかどうか。", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "alt_text": {"description": "添付ファイルが表示されないときに表示する代替テキスト。", "type": "string", "required": false}, "caption": {"description": "添付ファイルのキャプション。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、添付ファイルのキャプション。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、添付ファイルの HTML キャプション。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "添付ファイルの説明。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、添付ファイルの説明。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、添付ファイルの HTML の説明。", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "添付ファイルの関連投稿の ID。", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/post-process": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "添付ファイルの一意識別子。", "type": "integer", "required": false}, "action": {"type": "string", "enum": ["create-image-subsizes"], "required": true}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/edit": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"src": {"description": "編集された画像ファイルの URL。", "type": "string", "format": "uri", "required": true}, "modifiers": {"description": "画像編集の配列。", "type": "array", "minItems": 1, "items": {"description": "画像編集。", "type": "object", "required": ["type", "args"], "oneOf": [{"title": "回転", "properties": {"type": {"description": "回転のタイプ。", "type": "string", "enum": ["rotate"]}, "args": {"description": "回転の引数。", "type": "object", "required": ["angle"], "properties": {"angle": {"description": "時計回りに回転する角度。", "type": "number"}}}}}, {"title": "切り抜き", "properties": {"type": {"description": "切り抜きの種類。", "type": "string", "enum": ["crop"]}, "args": {"description": "切り抜きの引数。", "type": "object", "required": ["left", "top", "width", "height"], "properties": {"left": {"description": "画像の幅に対する、切り抜きを開始する左からの水平位置のパーセント指定。", "type": "number"}, "top": {"description": "画像の高さに対する、切り抜きを開始する上からの垂直位置のパーセント指定。", "type": "number"}, "width": {"description": "元の画像に対する、切り抜く画像の幅のパーセント指定。", "type": "number"}, "height": {"description": "元の画像に対する、切り抜く画像の高さのパーセント指定。", "type": "number"}}}}}]}, "required": false}, "rotation": {"description": "回転する画像の時計回りの角度。非推奨: 代わりに `modifiers` を使用してください。", "type": "integer", "minimum": 0, "exclusiveMinimum": true, "maximum": 360, "exclusiveMaximum": true, "required": false}, "x": {"description": "元の画像に対する、切り抜く開始位置の x 座標のパーセント指定。非推奨: 代わりに `modifiers` を使用してください。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "y": {"description": "元の画像に対する、切り抜く開始位置の y 座標のパーセント指定。非推奨: 代わりに `modifiers` を使用してください。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "width": {"description": "元の画像に対する、切り抜く画像の幅のパーセント指定。非推奨: 代わりに `modifiers` を使用してください。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "height": {"description": "元の画像に対する、切り抜く画像の高さのパーセント指定。非推奨: 代わりに `modifiers` を使用してください。", "type": "number", "minimum": 0, "maximum": 100, "required": false}}}]}, "/wp/v2/menu-items": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "menu_order", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "結果セットを複数のタクソノミー間の関係に基づいて限定します。", "type": "string", "enum": ["AND", "OR"], "required": false}, "menus": {"description": "「menus」タクソノミーに割り当てられた特定のタームを持つ項目に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "特定のタームのすべてに項目を割り当てるか、一部に割り当てるか。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "menus_exclude": {"description": "「menus」タクソノミーに割り当てられた特定のタームを持つ項目以外に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "menu_order": {"description": "特定の menu_order 値を持つ投稿に結果を限定します。", "type": "integer", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"title": {"description": "オブジェクトのタイトル。", "type": ["string", "object"], "properties": {"raw": {"description": "データベースに存在する形態の、オブジェクトのタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、オブジェクトの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"default": "custom", "description": "\"post_type\" や \"taxonomy\" のように、元々表わされていたオブジェクトのファミリー。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"default": "publish", "description": "オブジェクトに対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "parent": {"default": 0, "description": "親オブジェクトの ID。", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "このメニュー項目のリンク要素のタイトル属性のテキスト。", "type": "string", "required": false}, "classes": {"description": "このメニュー項目のリンク要素のクラス名。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "このメニュー項目の説明。", "type": "string", "required": false}, "menu_order": {"default": 1, "description": "この項目の親メニューである nav_menu_item の DB ID (もしあれば)。ない場合は0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "「カテゴリー」、「投稿」、「添付ファイル」など、元々表わされていたオブジェクトのタイプ。", "type": "string", "required": false}, "object_id": {"default": 0, "description": "このメニュー項目が表す元のオブジェクトのデータベース ID。例: 投稿の ID やカテゴリーの term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "このメニュー項目のリンク要素のターゲット属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "このメニュー項目が指す URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "このメニュー項目のリンクで表現された XFN 関係。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "タクソノミー「nav_menu」に属し、そのオブジェクトに割り当てられているターム。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/menu-items"}]}}, "/wp/v2/menu-items/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "title": {"description": "オブジェクトのタイトル。", "type": ["string", "object"], "properties": {"raw": {"description": "データベースに存在する形態の、オブジェクトのタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、オブジェクトの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "\"post_type\" や \"taxonomy\" のように、元々表わされていたオブジェクトのファミリー。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "オブジェクトに対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "parent": {"description": "親オブジェクトの ID。", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "このメニュー項目のリンク要素のタイトル属性のテキスト。", "type": "string", "required": false}, "classes": {"description": "このメニュー項目のリンク要素のクラス名。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "このメニュー項目の説明。", "type": "string", "required": false}, "menu_order": {"description": "この項目の親メニューである nav_menu_item の DB ID (もしあれば)。ない場合は0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "「カテゴリー」、「投稿」、「添付ファイル」など、元々表わされていたオブジェクトのタイプ。", "type": "string", "required": false}, "object_id": {"description": "このメニュー項目が表す元のオブジェクトのデータベース ID。例: 投稿の ID やカテゴリーの term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "このメニュー項目のリンク要素のターゲット属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "このメニュー項目が指す URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "このメニュー項目のリンクで表現された XFN 関係。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "タクソノミー「nav_menu」に属し、そのオブジェクトに割り当てられているターム。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/menu-items/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親オブジェクトの ID。", "type": "integer", "minimum": 0, "required": false}, "title": {"description": "オブジェクトのタイトル。", "type": ["string", "object"], "properties": {"raw": {"description": "データベースに存在する形態の、オブジェクトのタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、オブジェクトの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "\"post_type\" や \"taxonomy\" のように、元々表わされていたオブジェクトのファミリー。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "オブジェクトに対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "attr_title": {"description": "このメニュー項目のリンク要素のタイトル属性のテキスト。", "type": "string", "required": false}, "classes": {"description": "このメニュー項目のリンク要素のクラス名。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "このメニュー項目の説明。", "type": "string", "required": false}, "menu_order": {"description": "この項目の親メニューである nav_menu_item の DB ID (もしあれば)。ない場合は0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "「カテゴリー」、「投稿」、「添付ファイル」など、元々表わされていたオブジェクトのタイプ。", "type": "string", "required": false}, "object_id": {"description": "このメニュー項目が表す元のオブジェクトのデータベース ID。例: 投稿の ID やカテゴリーの term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "このメニュー項目のリンク要素のターゲット属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "このメニュー項目が指す URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "このメニュー項目のリンクで表現された XFN 関係。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "タクソノミー「nav_menu」に属し、そのオブジェクトに割り当てられているターム。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}}}]}, "/wp/v2/menu-items/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "結果セットを複数のタクソノミー間の関係に基づいて限定します。", "type": "string", "enum": ["AND", "OR"], "required": false}, "wp_pattern_category": {"description": "「wp_pattern_category」タクソノミーに割り当てられた特定のタームを持つ項目に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "特定のタームのすべてに項目を割り当てるか、一部に割り当てるか。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "wp_pattern_category_exclude": {"description": "「wp_pattern_category」タクソノミーに割り当てられた特定のタームを持つ項目以外に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "wp_pattern_category": {"description": "タクソノミー「wp_pattern_category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/blocks"}]}}, "/wp/v2/blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "デフォルトの抜粋の長さを上書きします。", "type": "integer", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "wp_pattern_category": {"description": "タクソノミー「wp_pattern_category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "リビジョンがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "投稿の抜粋。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿の抜粋。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML の抜粋。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "抜粋がパスワードで保護されているかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "inline_featured_image": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "wp_pattern_category": {"description": "タクソノミー「wp_pattern_category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "リビジョンがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "slug": {"description": "テンプレートを識別する固有のスラッグ。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "テンプレートのテーマ固有識別子。", "type": "string", "required": false}, "type": {"description": "テンプレートのタイプ", "type": "string", "required": false}, "content": {"description": "テンプレートのコンテンツ。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態のテンプレートのコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "テンプレートが使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "テンプレートのタイトル。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態の、テンプレートのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、テンプレートの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "テンプレートの説明。", "type": "string", "required": false}, "status": {"description": "テンプレートの状態。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "テンプレートの作成者の ID。", "type": "integer", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "指定した投稿 ID に制限します。", "type": "integer", "required": false}, "area": {"description": "特定のテンプレートパーツエリアに制限する。", "type": "string", "required": false}, "post_type": {"description": "テンプレートを取得する投稿タイプ。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "テンプレートを識別する固有のスラッグ。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "テンプレートのテーマ固有識別子。", "type": "string", "required": false}, "type": {"description": "テンプレートのタイプ", "type": "string", "required": false}, "content": {"default": "", "description": "テンプレートのコンテンツ。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態のテンプレートのコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "テンプレートが使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "テンプレートのタイトル。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態の、テンプレートのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、テンプレートの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "テンプレートの説明。", "type": "string", "required": false}, "status": {"default": "publish", "description": "テンプレートの状態。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "テンプレートの作成者の ID。", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/templates"}]}}, "/wp/v2/templates/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "以下のフォールバックを取得するためのテンプレートのスラッグ", "type": "string", "required": true}, "is_custom": {"description": "テンプレートがカスタムであるか、テンプレート階層の一部であるかを指定します", "type": "boolean", "required": false}, "template_prefix": {"description": "作成されたテンプレートの接頭辞。これはメインのテンプレートタイプを抽出するために使用されます (例: \"taxonomy-blocks\"では、\"taxonomy\" が抽出されます)。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/templates/lookup"}]}}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "slug": {"description": "テンプレートを識別する固有のスラッグ。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "テンプレートのテーマ固有識別子。", "type": "string", "required": false}, "type": {"description": "テンプレートのタイプ", "type": "string", "required": false}, "content": {"description": "テンプレートのコンテンツ。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態のテンプレートのコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "テンプレートが使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "テンプレートのタイトル。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態の、テンプレートのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、テンプレートの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "テンプレートの説明。", "type": "string", "required": false}, "status": {"description": "テンプレートの状態。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "テンプレートの作成者の ID。", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "リビジョンがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "slug": {"description": "テンプレートを識別する固有のスラッグ。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "テンプレートのテーマ固有識別子。", "type": "string", "required": false}, "type": {"description": "テンプレートのタイプ", "type": "string", "required": false}, "content": {"description": "テンプレートのコンテンツ。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態のテンプレートのコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "テンプレートが使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "テンプレートのタイトル。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態の、テンプレートのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、テンプレートの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "テンプレートの説明。", "type": "string", "required": false}, "status": {"description": "テンプレートの状態。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "テンプレートの作成者の ID。", "type": "integer", "required": false}, "area": {"description": "テンプレートパーツの使用を目的とした場所 (ヘッダー、フッター、など)", "type": "string", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "テンプレートの ID", "type": "string", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/template-parts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "指定した投稿 ID に制限します。", "type": "integer", "required": false}, "area": {"description": "特定のテンプレートパーツエリアに制限する。", "type": "string", "required": false}, "post_type": {"description": "テンプレートを取得する投稿タイプ。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "テンプレートを識別する固有のスラッグ。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "テンプレートのテーマ固有識別子。", "type": "string", "required": false}, "type": {"description": "テンプレートのタイプ", "type": "string", "required": false}, "content": {"default": "", "description": "テンプレートのコンテンツ。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態のテンプレートのコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "テンプレートが使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "テンプレートのタイトル。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態の、テンプレートのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、テンプレートの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "テンプレートの説明。", "type": "string", "required": false}, "status": {"default": "publish", "description": "テンプレートの状態。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "テンプレートの作成者の ID。", "type": "integer", "required": false}, "area": {"description": "テンプレートパーツの使用を目的とした場所 (ヘッダー、フッター、など)", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/template-parts"}]}}, "/wp/v2/template-parts/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "以下のフォールバックを取得するためのテンプレートのスラッグ", "type": "string", "required": true}, "is_custom": {"description": "テンプレートがカスタムであるか、テンプレート階層の一部であるかを指定します", "type": "boolean", "required": false}, "template_prefix": {"description": "作成されたテンプレートの接頭辞。これはメインのテンプレートタイプを抽出するために使用されます (例: \"taxonomy-blocks\"では、\"taxonomy\" が抽出されます)。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/template-parts/lookup"}]}}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "slug": {"description": "テンプレートを識別する固有のスラッグ。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "テンプレートのテーマ固有識別子。", "type": "string", "required": false}, "type": {"description": "テンプレートのタイプ", "type": "string", "required": false}, "content": {"description": "テンプレートのコンテンツ。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態のテンプレートのコンテンツ。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "テンプレートが使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "テンプレートのタイトル。", "type": ["object", "string"], "properties": {"raw": {"description": "データベースに存在する状態の、テンプレートのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、テンプレートの HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "テンプレートの説明。", "type": "string", "required": false}, "status": {"description": "テンプレートの状態。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "テンプレートの作成者の ID。", "type": "integer", "required": false}, "area": {"description": "テンプレートパーツの使用を目的とした場所 (ヘッダー、フッター、など)", "type": "string", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "グローバルスタイルリビジョンの親の ID。", "type": "integer", "required": false}, "id": {"description": "グローバルスタイルリビジョンの一意の識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[\\/\\s%\\w\\.\\(\\)\\[\\]\\@_\\-]+)/variations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "テーマ識別子", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "テーマ識別子", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/(?P<id>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"id": {"description": "テンプレートの ID", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": false}, "args": {"styles": {"description": "グローバルスタイル。", "type": ["object"], "required": false}, "settings": {"description": "グローバル設定。", "type": ["object"], "required": false}, "title": {"description": "グローバルスタイルバリエーションのタイトル", "type": ["object", "string"], "properties": {"raw": {"description": "データベース内に存在する形での、グローバルスタイルバリエーションのタイトル。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}}}]}, "/wp/v2/navigation": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/navigation"}]}}, "/wp/v2/navigation/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "オブジェクト属性でコレクションを並び替えます。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "親リビジョンの ID。", "type": "integer", "required": false}, "id": {"description": "リビジョンの固有識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "リビジョンがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/navigation/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/font-families": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "args": {"theme_json_version": {"description": "タイポグラフィ設定に使用される theme.json スキーマのバージョン。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "文字列でエンコードされた theme.json 形式の font-family の宣言。", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/font-families"}]}}, "/wp/v2/font-families/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "theme_json_version": {"description": "タイポグラフィ設定に使用される theme.json スキーマのバージョン。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "文字列でエンコードされた theme.json 形式の font-family の宣言。", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "フォントフェイスの親フォントファミリーの ID。", "type": "integer", "required": true}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}}}, {"methods": ["POST"], "args": {"font_family_id": {"description": "フォントフェイスの親フォントファミリーの ID。", "type": "integer", "required": true}, "theme_json_version": {"description": "タイポグラフィ設定に使用される theme.json スキーマのバージョン。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_face_settings": {"description": "文字列でエンコードされた theme.json 形式の font-face の宣言。", "type": "string", "required": true}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "フォントフェイスの親フォントファミリーの ID。", "type": "integer", "required": true}, "id": {"description": "フォントフェイスの一意の識別子。", "type": "integer", "required": true}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"font_family_id": {"description": "フォントフェイスの親フォントファミリーの ID。", "type": "integer", "required": true}, "id": {"description": "フォントフェイスの一意の識別子。", "type": "integer", "required": true}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/column": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "特定の親 ID に属する項目に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "特定の親 ID に属さない項目に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "parent": {"description": "親投稿の ID。", "type": "integer", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/column"}]}}, "/wp/v2/column/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "parent": {"description": "親投稿の ID。", "type": "integer", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/column/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親投稿の ID。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}}}]}, "/wp/v2/column/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/news": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "入力された ISO8601 準拠の日付より後に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "入力された ISO8601 準拠の日付より前に更新された投稿にレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "検索入力を解釈する方法です。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つ投稿に結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "一つ以上のステータスが割り当てられた投稿に結果を限定します。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "結果セットを複数のタクソノミー間の関係に基づいて限定します。", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "「categories」タクソノミーに割り当てられた特定のタームを持つ項目に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "結果を限定したタームに子タームを含めるかどうか。", "type": "boolean", "default": false}, "operator": {"description": "特定のタームのすべてに項目を割り当てるか、一部に割り当てるか。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "「categories」タクソノミーに割り当てられた特定のタームを持つ項目以外に結果を限定します。", "type": ["object", "array"], "oneOf": [{"title": "ターム ID 一覧", "description": "リストした ID と合致するターム。", "type": "array", "items": {"type": "integer"}}, {"title": "ターム ID タクソノミークエリー", "description": "高度なタームクエリーを実行します。", "type": "object", "properties": {"terms": {"description": "ターム ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "結果を限定したタームに子タームを含めるかどうか。", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "categories": {"description": "タクソノミー「category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/news"}]}}, "/wp/v2/news/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "categories": {"description": "タクソノミー「category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "投稿の一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}}}]}, "/wp/v2/news/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "date": {"description": "投稿の公開日 (サイトのタイムゾーン)。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "投稿の公開日 (GMT)。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "そのタイプに特有な、投稿の英数字識別子。", "type": "string", "required": false}, "status": {"description": "投稿に対して名前がついているステータス。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "コンテンツや抜粋へのアクセスを保護するパスワード。", "type": "string", "required": false}, "title": {"description": "投稿のタイトル。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のタイトル。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に整形された、投稿の HTML タイトル。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "投稿のコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、投稿のコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、投稿の HTML コンテンツ。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "投稿が使用するコンテンツブロック形式のバージョン。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "コンテンツをパスワードで保護するかどうか。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "投稿のアイキャッチとなるメディアの ID。", "type": "integer", "required": false}, "template": {"description": "投稿を表示するために使用するテーマファイル。", "type": "string", "required": false}, "categories": {"description": "タクソノミー「category」に属し、その投稿に割り当てられているターム。", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/news/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "親自動保存の ID。", "type": "integer", "required": false}, "id": {"description": "自動保存の ID。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/types"}]}}, "/wp/v2/types/(?P<type>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"description": "投稿タイプの英数字の識別子。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/statuses": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/statuses"}]}}, "/wp/v2/statuses/(?P<status>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "ステータスの英数字の識別子。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/taxonomies": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "type": {"description": "特定の投稿タイプに関連付けられたタクソノミーに結果を制限します。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/taxonomies"}]}}, "/wp/v2/taxonomies/(?P<taxonomy>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"description": "タクソノミーの英数字の識別子。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/categories": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "タームの属性でコレクションを並べ替えます。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "どの投稿にも割り当てられていないタームを非表示にするかどうか。", "type": "boolean", "default": false, "required": false}, "parent": {"description": "特定の親に割り当てられたタームに結果を限定します。", "type": "integer", "required": false}, "post": {"description": "特定の投稿に割り当てられたタームに結果を限定します。", "type": "integer", "default": null, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つタームに結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": true}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "parent": {"description": "親のターム ID。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/categories"}]}}, "/wp/v2/categories/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": false}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "parent": {"description": "親のターム ID。", "type": "integer", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "タームがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/tags": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "タームの属性でコレクションを並べ替えます。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "どの投稿にも割り当てられていないタームを非表示にするかどうか。", "type": "boolean", "default": false, "required": false}, "post": {"description": "特定の投稿に割り当てられたタームに結果を限定します。", "type": "integer", "default": null, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つタームに結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": true}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/tags"}]}}, "/wp/v2/tags/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": false}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "タームがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/menus": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "タームの属性でコレクションを並べ替えます。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "どの投稿にも割り当てられていないタームを非表示にするかどうか。", "type": "boolean", "default": false, "required": false}, "post": {"description": "特定の投稿に割り当てられたタームに結果を限定します。", "type": "integer", "default": null, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つタームに結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": true}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}, "locations": {"description": "メニューに割り当てられた位置。", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "自動的にトップレベルのページをこのメニューに追加するかどうか。", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/menus"}]}}, "/wp/v2/menus/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": false}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}, "locations": {"description": "メニューに割り当てられた位置。", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "自動的にトップレベルのページをこのメニューに追加するかどうか。", "type": "boolean", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "タームがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/wp_pattern_category": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "タームの属性でコレクションを並べ替えます。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "どの投稿にも割り当てられていないタームを非表示にするかどうか。", "type": "boolean", "default": false, "required": false}, "post": {"description": "特定の投稿に割り当てられたタームに結果を限定します。", "type": "integer", "default": null, "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つタームに結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": true}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/wp_pattern_category"}]}}, "/wp/v2/wp_pattern_category/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "description": {"description": "タームの HTML の説明。", "type": "string", "required": false}, "name": {"description": "タームの HTML タイトル。", "type": "string", "required": false}, "slug": {"description": "そのタイプに特有な、タームの英数字識別子。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "タームの一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "タームがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}}}]}, "/wp/v2/users": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"default": "asc", "description": "属性で昇順または降順に並べ替えます。", "enum": ["asc", "desc"], "type": "string", "required": false}, "orderby": {"default": "name", "description": "ユーザーの属性でコレクションを並べ替えます。", "enum": ["id", "include", "name", "registered_date", "slug", "include_slugs", "email", "url"], "type": "string", "required": false}, "slug": {"description": "一つ以上の特定のスラッグを持つユーザーに結果を限定します。", "type": "array", "items": {"type": "string"}, "required": false}, "roles": {"description": "入力された、少なくとも一つ以上の特定の権限グループに一致するユーザーに結果を限定します。csv ファイルのリスト、あるいは一つの権限グループも可能です。", "type": "array", "items": {"type": "string"}, "required": false}, "capabilities": {"description": "提供された少なくとも一つ以上の特定の権限に一致するユーザーに結果セットを限定します。csv ファイルのリスト、あるいは一つの権限も可能です。", "type": "array", "items": {"type": "string"}, "required": false}, "who": {"description": "投稿者とみなされるユーザーに結果を限定する。", "type": "string", "enum": ["authors"], "required": false}, "has_published_posts": {"description": "投稿を公開したユーザーに結果を限定する。", "type": ["boolean", "array"], "items": {"type": "string", "enum": {"post": "post", "page": "page", "attachment": "attachment", "nav_menu_item": "nav_menu_item", "wp_block": "wp_block", "wp_template": "wp_template", "wp_template_part": "wp_template_part", "wp_global_styles": "wp_global_styles", "wp_navigation": "wp_navigation", "wp_font_family": "wp_font_family", "wp_font_face": "wp_font_face", "column": "column", "news": "news"}}, "required": false}, "search_columns": {"default": [], "description": "検索する列名の配列。", "type": "array", "items": {"enum": ["email", "name", "id", "username", "slug"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"username": {"description": "ユーザーのログイン名。", "type": "string", "required": true}, "name": {"description": "ユーザーの、ブログ上の表示名。", "type": "string", "required": false}, "first_name": {"description": "ユーザーの名。", "type": "string", "required": false}, "last_name": {"description": "ユーザーの姓。", "type": "string", "required": false}, "email": {"description": "ユーザーのメールアドレス。", "type": "string", "format": "email", "required": true}, "url": {"description": "ユーザーの URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "ユーザーの説明。", "type": "string", "required": false}, "locale": {"description": "ユーザーのロケール。", "type": "string", "enum": ["", "en_US", "ja"], "required": false}, "nickname": {"description": "ユーザーのニックネーム。", "type": "string", "required": false}, "slug": {"description": "ユーザーの英数字の識別子。", "type": "string", "required": false}, "roles": {"description": "ユーザーに割り当てられた権限グループ。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "ユーザーのパスワード (含まれることはありません)。", "type": "string", "required": true}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "設定が更新された日時です。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/users"}]}}, "/wp/v2/users/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "ユーザーの一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "ユーザーの一意識別子。", "type": "integer", "required": false}, "username": {"description": "ユーザーのログイン名。", "type": "string", "required": false}, "name": {"description": "ユーザーの、ブログ上の表示名。", "type": "string", "required": false}, "first_name": {"description": "ユーザーの名。", "type": "string", "required": false}, "last_name": {"description": "ユーザーの姓。", "type": "string", "required": false}, "email": {"description": "ユーザーのメールアドレス。", "type": "string", "format": "email", "required": false}, "url": {"description": "ユーザーの URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "ユーザーの説明。", "type": "string", "required": false}, "locale": {"description": "ユーザーのロケール。", "type": "string", "enum": ["", "en_US", "ja"], "required": false}, "nickname": {"description": "ユーザーのニックネーム。", "type": "string", "required": false}, "slug": {"description": "ユーザーの英数字の識別子。", "type": "string", "required": false}, "roles": {"description": "ユーザーに割り当てられた権限グループ。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "ユーザーのパスワード (含まれることはありません)。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "設定が更新された日時です。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "ユーザーの一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ユーザーがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}, "reassign": {"type": "integer", "description": "削除するユーザーの投稿とリンクを、指定 ID のユーザーに割り当てます。", "required": true}}}]}, "/wp/v2/users/me": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"username": {"description": "ユーザーのログイン名。", "type": "string", "required": false}, "name": {"description": "ユーザーの、ブログ上の表示名。", "type": "string", "required": false}, "first_name": {"description": "ユーザーの名。", "type": "string", "required": false}, "last_name": {"description": "ユーザーの姓。", "type": "string", "required": false}, "email": {"description": "ユーザーのメールアドレス。", "type": "string", "format": "email", "required": false}, "url": {"description": "ユーザーの URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "ユーザーの説明。", "type": "string", "required": false}, "locale": {"description": "ユーザーのロケール。", "type": "string", "enum": ["", "en_US", "ja"], "required": false}, "nickname": {"description": "ユーザーのニックネーム。", "type": "string", "required": false}, "slug": {"description": "ユーザーの英数字の識別子。", "type": "string", "required": false}, "roles": {"description": "ユーザーに割り当てられた権限グループ。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "ユーザーのパスワード (含まれることはありません)。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "設定が更新された日時です。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}, {"methods": ["DELETE"], "args": {"force": {"type": "boolean", "default": false, "description": "ユーザーがゴミ箱機能に対応していないため、true でなければなりません。", "required": false}, "reassign": {"type": "integer", "description": "削除するユーザーの投稿とリンクを、指定 ID のユーザーに割り当てます。", "required": true}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/users/me"}]}}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords": {"namespace": "wp/v2", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"app_id": {"description": "アプリケーションが提供した固有識別用 UUID。UUID v5を URL または DNS の名前空間と共に使うことをおすすめします。", "type": "string", "format": "uuid", "required": false}, "name": {"description": "アプリケーションパスワードの名称。", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": true}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/introspect": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/(?P<uuid>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"app_id": {"description": "アプリケーションが提供した固有識別用 UUID。UUID v5を URL または DNS の名前空間と共に使うことをおすすめします。", "type": "string", "format": "uuid", "required": false}, "name": {"description": "アプリケーションパスワードの名称。", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": false}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/comments": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "after": {"description": "入力された ISO8601 準拠の日付より後に公開されたコメントにレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "特定のユーザー ID が割り当てられたコメントに結果を限定します。認証が必要です。", "type": "array", "items": {"type": "integer"}, "required": false}, "author_exclude": {"description": "特定のユーザー ID が割り当てられたコメントを結果から除外します。認証が必要です。", "type": "array", "items": {"type": "integer"}, "required": false}, "author_email": {"default": null, "description": "特定の投稿者のメールからのものに結果を限定します。認証が必要です。", "format": "email", "type": "string", "required": false}, "before": {"description": "入力された ISO8601 準拠の日付より前に公開されたコメントにレスポンスを限定します。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "コメントの属性でコレクションを並べ替えます。", "type": "string", "default": "date_gmt", "enum": ["date", "date_gmt", "id", "include", "post", "parent", "type"], "required": false}, "parent": {"default": [], "description": "特定の親 ID に属するコメントに結果を限定します。", "type": "array", "items": {"type": "integer"}, "required": false}, "parent_exclude": {"default": [], "description": "特定の親 ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "required": false}, "post": {"default": [], "description": "特定の投稿 ID に割り当てられたコメントに結果を限定します。", "type": "array", "items": {"type": "integer"}, "required": false}, "status": {"default": "approve", "description": "特定のステータスが割り当てられたコメントに結果を限定します。認証が必要です。", "type": "string", "required": false}, "type": {"default": "comment", "description": "特定のタイプが割り当てられたコメントに結果を限定します。認証が必要です。", "type": "string", "required": false}, "password": {"description": "パスワードで保護されている場合の投稿のパスワード。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"author": {"description": "投稿者がユーザーだった場合の、ユーザーオブジェクトの ID。", "type": "integer", "required": false}, "author_email": {"description": "コメント投稿者のメールアドレス。", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "コメント投稿者の IP アドレス。", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "コメント作成者の名前を表示します。", "type": "string", "required": false}, "author_url": {"description": "コメント投稿者の URL。", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "コメント投稿者のユーザーエージェント。", "type": "string", "required": false}, "content": {"description": "コメントのコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、コメントのコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、コメントの HTML コンテンツ。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "コメントの公開日 (サイトのタイムゾーン)。", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "コメントの公開日 (GMT)。", "type": "string", "format": "date-time", "required": false}, "parent": {"default": 0, "description": "親コメントの ID。", "type": "integer", "required": false}, "post": {"default": 0, "description": "関連投稿オブジェクトの ID。", "type": "integer", "required": false}, "status": {"description": "コメントのステータス。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/comments"}]}}, "/wp/v2/comments/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "コメントの一意識別子。", "type": "integer", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "コメントの親投稿のパスワード (投稿がパスワード保護されている場合)。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "コメントの一意識別子。", "type": "integer", "required": false}, "author": {"description": "投稿者がユーザーだった場合の、ユーザーオブジェクトの ID。", "type": "integer", "required": false}, "author_email": {"description": "コメント投稿者のメールアドレス。", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "コメント投稿者の IP アドレス。", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "コメント作成者の名前を表示します。", "type": "string", "required": false}, "author_url": {"description": "コメント投稿者の URL。", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "コメント投稿者のユーザーエージェント。", "type": "string", "required": false}, "content": {"description": "コメントのコンテンツ。", "type": "object", "properties": {"raw": {"description": "データベースに存在する形態の、コメントのコンテンツ。", "type": "string", "context": ["edit"]}, "rendered": {"description": "表示用に変換された、コメントの HTML コンテンツ。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "コメントの公開日 (サイトのタイムゾーン)。", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "コメントの公開日 (GMT)。", "type": "string", "format": "date-time", "required": false}, "parent": {"description": "親コメントの ID。", "type": "integer", "required": false}, "post": {"description": "関連投稿オブジェクトの ID。", "type": "integer", "required": false}, "status": {"description": "コメントのステータス。", "type": "string", "required": false}, "meta": {"description": "メタフィールド。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "コメントの一意識別子。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "ゴミ箱に入れずに強制的に削除するかどうか。", "required": false}, "password": {"description": "コメントの親投稿のパスワード (投稿がパスワード保護されている場合)。", "type": "string", "required": false}}}]}, "/wp/v2/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "type": {"default": "post", "description": "オブジェクトタイプのアイテムの結果を制限します。", "type": "string", "enum": ["post", "term", "post-format"], "required": false}, "subtype": {"default": "any", "description": "1つ以上のオブジェクトサブタイプのアイテムの結果を制限します。", "type": "array", "items": {"enum": ["post", "page", "column", "news", "category", "post_tag", "any"], "type": "string"}, "required": false}, "exclude": {"description": "特定の ID を結果から除外します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "特定の ID に結果を限定します。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/search"}]}}, "/wp/v2/block-renderer/(?P<name>[a-z0-9-]+/[a-z0-9-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET", "POST"], "args": {"name": {"description": "ブロックのユニークな登録名。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["edit"], "default": "view", "required": false}, "attributes": {"description": "ブロックの属性。", "type": "object", "default": [], "required": false}, "post_id": {"description": "投稿コンテキストの ID。", "type": "integer", "required": false}}}]}, "/wp/v2/block-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "ブロック名前空間。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/block-types"}]}}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "ブロック名前空間。", "type": "string", "required": false}}}]}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)/(?P<name>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"name": {"description": "ブロック名。", "type": "string", "required": false}, "namespace": {"description": "ブロック名前空間。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/settings": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"title": {"title": "タイトル", "description": "サイト名。", "type": "string", "required": false}, "description": {"title": "キャッチフレーズ", "description": "サイトのキャッチフレーズ。", "type": "string", "required": false}, "url": {"title": "", "description": "サイト URL。", "type": "string", "format": "uri", "required": false}, "email": {"title": "", "description": "このアドレスは新規ユーザーの通知などサイト管理のために使われます。", "type": "string", "format": "email", "required": false}, "timezone": {"title": "", "description": "現在地と同じタイムゾーンの都市。", "type": "string", "required": false}, "date_format": {"title": "", "description": "日付文字列の書式。", "type": "string", "required": false}, "time_format": {"title": "", "description": "時刻文字列の書式。", "type": "string", "required": false}, "start_of_week": {"title": "", "description": "週の始まりの曜日番号。", "type": "integer", "required": false}, "language": {"title": "", "description": "WordPress のロケールコード。", "type": "string", "required": false}, "use_smilies": {"title": "", "description": ":-) や :-P などの顔文字を絵文字に変換します。", "type": "boolean", "required": false}, "default_category": {"title": "", "description": "デフォルトの投稿カテゴリー。", "type": "integer", "required": false}, "default_post_format": {"title": "", "description": "デフォルトの投稿フォーマット。", "type": "string", "required": false}, "posts_per_page": {"title": "ページごとの最大投稿数", "description": "表示する最大投稿数。", "type": "integer", "required": false}, "show_on_front": {"title": "フロントページでの表示", "description": "フロントページに表示する内容", "type": "string", "required": false}, "page_on_front": {"title": "フロントページのページ", "description": "フロントページに表示するページの ID", "type": "integer", "required": false}, "page_for_posts": {"title": "", "description": "最新の投稿を表示するページの ID", "type": "integer", "required": false}, "default_ping_status": {"title": "", "description": "新しい記事に対し他のブログからの通知 (ピンバック・トラックバック) を受け付ける。", "type": "string", "enum": ["open", "closed"], "required": false}, "default_comment_status": {"title": "新規投稿へのコメントを許可する", "description": "新しい投稿へのコメントを許可する。", "type": "string", "enum": ["open", "closed"], "required": false}, "site_logo": {"title": "ロゴ", "description": "サイトロゴ。", "type": "integer", "required": false}, "site_icon": {"title": "アイコン", "description": "サイトアイコン。", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/settings"}]}}, "/wp/v2/themes": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "1つ以上のステータスが割り当てられたテーマに結果を絞り込む。", "type": "array", "items": {"enum": ["active", "inactive"], "type": "string"}, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/themes"}]}}, "/wp/v2/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"stylesheet": {"description": "テーマのスタイルシート。テーマを一意に識別します。", "type": "string", "required": false}}}]}, "/wp/v2/plugins": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "required": false}, "status": {"description": "指定したステータスに一致するプラグインのみに結果を制限します。", "type": "array", "items": {"type": "string", "enum": ["inactive", "active"]}, "required": false}}}, {"methods": ["POST"], "args": {"slug": {"type": "string", "description": "WordPress.org プラグインディレクトリのスラッグ。", "pattern": "[\\w\\-]+", "required": true}, "status": {"description": "プラグインの有効化ステータス。", "type": "string", "enum": ["inactive", "active"], "default": "inactive", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/plugins"}]}}, "/wp/v2/plugins/(?P<plugin>[^.\\/]+(?:\\/[^.\\/]+)?)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "status": {"description": "プラグインの有効化ステータス。", "type": "string", "enum": ["inactive", "active"], "required": false}}}, {"methods": ["DELETE"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}]}, "/wp/v2/sidebars": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/sidebars"}]}}, "/wp/v2/sidebars/(?P<id>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "登録されたサイドバーの ID", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"widgets": {"description": "ネストしたウィジェット。", "type": "array", "items": {"type": ["object", "string"]}, "required": false}}}]}, "/wp/v2/widget-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/widget-types"}]}}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "ウィジェットタイプ ID。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/encode": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "ウィジェットタイプ ID。", "type": "string", "required": true}, "instance": {"description": "ウィジェットの現在のインスタンス設定。", "type": "object", "required": false}, "form_data": {"description": "インスタンス設定にエンコードされるシリアル化されたウィジェットのフォームデータ。", "type": "string", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/render": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "ウィジェットタイプ ID。", "type": "string", "required": true}, "instance": {"description": "ウィジェットの現在のインスタンス設定。", "type": "object", "required": false}}}]}, "/wp/v2/widgets": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "sidebar": {"description": "ウィジェットを戻すためのサイドバー。", "type": "string", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"id": {"description": "ウィジェットの固有識別子。", "type": "string", "required": false}, "id_base": {"description": "ウィジェットのタイプ。widget-types エンドポイントの ID と対応します。", "type": "string", "required": false}, "sidebar": {"default": "wp_inactive_widgets", "description": "ウィジェットが属するサイドバー。", "type": "string", "required": true}, "instance": {"description": "サポートしている場合、ウィジェットのインスタンス設定。", "type": "object", "properties": {"encoded": {"description": "インスタンス設定の Base64 エンコード表現。", "type": "string", "context": ["edit"]}, "hash": {"description": "インスタンス設定の暗号化ハッシュ。", "type": "string", "context": ["edit"]}, "raw": {"description": "サポートしている場合、デコードしたインスタンス設定。", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "ウィジェット管理フォームから URL エンコードしたフォームデータ。インスタンスをサポートしないウィジェットの更新に使用されます。書き込みのみ。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/widgets"}]}}, "/wp/v2/widgets/(?P<id>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "ウィジェットの固有識別子。", "type": "string", "required": false}, "id_base": {"description": "ウィジェットのタイプ。widget-types エンドポイントの ID と対応します。", "type": "string", "required": false}, "sidebar": {"description": "ウィジェットが属するサイドバー。", "type": "string", "required": false}, "instance": {"description": "サポートしている場合、ウィジェットのインスタンス設定。", "type": "object", "properties": {"encoded": {"description": "インスタンス設定の Base64 エンコード表現。", "type": "string", "context": ["edit"]}, "hash": {"description": "インスタンス設定の暗号化ハッシュ。", "type": "string", "context": ["edit"]}, "raw": {"description": "サポートしている場合、デコードしたインスタンス設定。", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "ウィジェット管理フォームから URL エンコードしたフォームデータ。インスタンスをサポートしないウィジェットの更新に使用されます。書き込みのみ。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"force": {"description": "強制的にウィジェットを削除するか、無効化サイドバーに移動するかどうか。", "type": "boolean", "required": false}}}]}, "/wp/v2/block-directory/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "term": {"description": "検索キーワードに一致するブロックのみに結果を制限します。", "type": "string", "minLength": 1, "required": true}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/block-directory/search"}]}}, "/wp/v2/pattern-directory/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "文字列に一致するものに結果を限定します。", "type": "string", "minLength": 1, "required": false}, "category": {"description": "カテゴリー ID に一致するものに結果を限定します。", "type": "integer", "minimum": 1, "required": false}, "keyword": {"description": "キーワード ID に一致するものに結果を限定します。", "type": "integer", "minimum": 1, "required": false}, "slug": {"description": "パターン (スラッグ) に一致するものに結果を限定します。", "type": "array", "required": false}, "offset": {"description": "特定の項目数で結果をオフセットします。", "type": "integer", "required": false}, "order": {"description": "属性で昇順または降順に並べ替えます。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "投稿の属性でコレクションを並べ替えます。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "favorite_count"], "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/pattern-directory/patterns"}]}}, "/wp/v2/block-patterns/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/block-patterns/patterns"}]}}, "/wp/v2/block-patterns/categories": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/block-patterns/categories"}]}}, "/wp-site-health/v1": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-site-health/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1"}]}}, "/wp-site-health/v1/tests/background-updates": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/tests/background-updates"}]}}, "/wp-site-health/v1/tests/loopback-requests": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/tests/loopback-requests"}]}}, "/wp-site-health/v1/tests/https-status": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/tests/https-status"}]}}, "/wp-site-health/v1/tests/dotorg-communication": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/tests/dotorg-communication"}]}}, "/wp-site-health/v1/tests/authorization-header": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/tests/authorization-header"}]}}, "/wp-site-health/v1/directory-sizes": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/directory-sizes"}]}}, "/wp-site-health/v1/tests/page-cache": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-site-health/v1/tests/page-cache"}]}}, "/wp-block-editor/v1": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-block-editor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-block-editor/v1"}]}}, "/wp-block-editor/v1/url-details": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "処理する URL。", "type": "string", "format": "uri", "required": true}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-block-editor/v1/url-details"}]}}, "/wp/v2/menu-locations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/menu-locations"}]}}, "/wp/v2/menu-locations/(?P<location>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"location": {"description": "メニュー位置の英数字の識別子。", "type": "string", "required": false}, "context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp-block-editor/v1/export": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-block-editor/v1/export"}]}}, "/wp-block-editor/v1/navigation-fallback": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp-block-editor/v1/navigation-fallback"}]}}, "/wp/v2/font-collections": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "コレクションの現在のページ。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "結果として返される項目の最大数。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}}}], "_links": {"self": [{"href": "https://www.tasku.co.jp/wp-json/wp/v2/font-collections"}]}}, "/wp/v2/font-collections/(?P<slug>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "このリクエストが作成されたスコープ。レスポンスに含まれるフィールドはスコープにより異なります。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}}, "site_logo": 0, "site_icon": 353, "site_icon_url": "https://www.tasku.co.jp/wp/wp-content/uploads/2024/08/cropped-favicon.png", "_links": {"help": [{"href": "https://developer.wordpress.org/rest-api/"}], "wp:featuredmedia": [{"embeddable": true, "type": "site_icon", "href": "https://www.tasku.co.jp/wp-json/wp/v2/media/353"}], "curies": [{"name": "wp", "href": "https://api.w.org/{rel}", "templated": true}]}}